package com.lisong.service;

import com.lisong.entity.*;
import com.lisong.dto.PlayHistoryRequest;
import com.lisong.mapper.*;
import com.lisong.service.impl.PlayServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 播放服务测试
 */
@ExtendWith(MockitoExtension.class)
public class PlayServiceTest {

    @Mock
    private MovieMapper movieMapper;

    @Mock
    private TvSeriesMapper tvSeriesMapper;

    @Mock
    private TvSeriesEpisodeMapper tvSeriesEpisodeMapper;

    @Mock
    private VarietyShowMapper varietyShowMapper;

    @Mock
    private VarietyShowEpisodeMapper varietyShowEpisodeMapper;

    @Mock
    private PlayHistoryMapper playHistoryMapper;

    @InjectMocks
    private PlayServiceImpl playService;

    @Test
    public void testGetMoviePlayUrl() {
        // 准备测试数据
        Movie movie = new Movie();
        movie.setId(1L);
        movie.setTitle("阿凡达：水之道");
        movie.setDuration(192); // 分钟

        // Mock数据库查询
        when(movieMapper.findById(1L)).thenReturn(movie);

        // 执行测试
        PlayResponse response = playService.getPlayUrl("movie", 1L, null, null);

        // 验证结果
        assertNotNull(response);
        assertEquals("1080p", response.getQuality());
        assertEquals(11520, response.getDuration()); // 192 * 60 = 11520秒
        assertTrue(response.getPlayUrl().contains("movie/1"));
        assertEquals(2, response.getSubtitles().size());
        
        // 验证方法调用
        verify(movieMapper, times(1)).findById(1L);
    }

    @Test
    public void testGetTvSeriesPlayUrl() {
        // 准备测试数据
        TvSeries tvSeries = new TvSeries();
        tvSeries.setId(1L);
        tvSeries.setTitle("狂飙");

        TvSeriesEpisode episode = new TvSeriesEpisode();
        episode.setId(1L);
        episode.setSeriesId(1L);
        episode.setEpisodeNumber(2);
        episode.setTitle("第2集");
        episode.setDuration(45); // 分钟

        // Mock数据库查询
        when(tvSeriesMapper.findById(1L)).thenReturn(tvSeries);
        when(tvSeriesEpisodeMapper.findBySeriesIdAndEpisode(1L, 2)).thenReturn(episode);

        // 执行测试
        PlayResponse response = playService.getPlayUrl("tv", 1L, 2, "720p");

        // 验证结果
        assertNotNull(response);
        assertEquals("720p", response.getQuality());
        assertEquals(2700, response.getDuration()); // 45 * 60 = 2700秒
        assertTrue(response.getPlayUrl().contains("tv/1"));
        assertTrue(response.getPlayUrl().contains("episode_2"));
        assertTrue(response.getPlayUrl().contains("720p"));
        
        // 验证方法调用
        verify(tvSeriesMapper, times(1)).findById(1L);
        verify(tvSeriesEpisodeMapper, times(1)).findBySeriesIdAndEpisode(1L, 2);
    }

    @Test
    public void testGetVarietyShowPlayUrl() {
        // 准备测试数据
        VarietyShow varietyShow = new VarietyShow();
        varietyShow.setId(1L);
        varietyShow.setTitle("向往的生活");

        VarietyShowEpisode episode = new VarietyShowEpisode();
        episode.setId(1L);
        episode.setShowId(1L);
        episode.setEpisodeNumber(1);
        episode.setTitle("第1期");
        episode.setDuration(90); // 分钟

        // Mock数据库查询
        when(varietyShowMapper.findById(1L)).thenReturn(varietyShow);
        when(varietyShowEpisodeMapper.findByShowIdAndEpisode(1L, 1)).thenReturn(episode);

        // 执行测试
        PlayResponse response = playService.getPlayUrl("variety", 1L, 1, null);

        // 验证结果
        assertNotNull(response);
        assertEquals("1080p", response.getQuality()); // 默认清晰度
        assertEquals(5400, response.getDuration()); // 90 * 60 = 5400秒
        assertTrue(response.getPlayUrl().contains("variety/1"));
        
        // 验证方法调用
        verify(varietyShowMapper, times(1)).findById(1L);
        verify(varietyShowEpisodeMapper, times(1)).findByShowIdAndEpisode(1L, 1);
    }

    @Test
    public void testGetPlayUrlWithInvalidType() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> playService.getPlayUrl("invalid", 1L, null, null)
        );
        
        assertEquals("不支持的内容类型: invalid", exception.getMessage());
    }

    @Test
    public void testGetMoviePlayUrlNotFound() {
        // Mock数据库查询返回null
        when(movieMapper.findById(999L)).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> playService.getPlayUrl("movie", 999L, null, null)
        );
        
        assertEquals("电影不存在", exception.getMessage());
    }

    @Test
    public void testRecordPlayHistoryNewRecord() {
        // 准备测试数据
        PlayHistoryRequest request = new PlayHistoryRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setEpisode(1);
        request.setProgress(3600);
        request.setDuration(7200);

        Long userId = 7L;

        // Mock数据库查询（不存在历史记录）
        when(playHistoryMapper.findByUserAndContent(userId, 1L, "movie", 1)).thenReturn(null);
        when(playHistoryMapper.insert(any(PlayHistory.class))).thenReturn(1);

        // 执行测试
        PlayHistory result = playService.recordPlayHistory(userId, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(userId, result.getUserId());
        assertEquals(1L, result.getContentId());
        assertEquals("movie", result.getContentType());
        assertEquals(1, result.getEpisode());
        assertEquals(3600, result.getProgress());
        assertEquals(7200, result.getDuration());
        assertEquals("1080p", result.getQuality());
        
        // 验证方法调用
        verify(playHistoryMapper, times(1)).findByUserAndContent(userId, 1L, "movie", 1);
        verify(playHistoryMapper, times(1)).insert(any(PlayHistory.class));
        verify(playHistoryMapper, never()).update(any(PlayHistory.class));
    }

    @Test
    public void testRecordPlayHistoryUpdateExisting() {
        // 准备测试数据
        PlayHistoryRequest request = new PlayHistoryRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setEpisode(1);
        request.setProgress(5400);
        request.setDuration(7200);

        Long userId = 7L;

        PlayHistory existingHistory = new PlayHistory();
        existingHistory.setId(1L);
        existingHistory.setUserId(userId);
        existingHistory.setContentId(1L);
        existingHistory.setContentType("movie");
        existingHistory.setEpisode(1);
        existingHistory.setProgress(3600);
        existingHistory.setDuration(7200);
        existingHistory.setWatchedAt(LocalDateTime.now().minusHours(1));

        // Mock数据库查询（存在历史记录）
        when(playHistoryMapper.findByUserAndContent(userId, 1L, "movie", 1)).thenReturn(existingHistory);
        when(playHistoryMapper.update(any(PlayHistory.class))).thenReturn(1);

        // 执行测试
        PlayHistory result = playService.recordPlayHistory(userId, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(5400, result.getProgress()); // 更新后的进度
        assertEquals(7200, result.getDuration());
        
        // 验证方法调用
        verify(playHistoryMapper, times(1)).findByUserAndContent(userId, 1L, "movie", 1);
        verify(playHistoryMapper, times(1)).update(any(PlayHistory.class));
        verify(playHistoryMapper, never()).insert(any(PlayHistory.class));
    }

    @Test
    public void testRecordPlayHistoryWithDefaultEpisode() {
        // 准备测试数据（不设置episode）
        PlayHistoryRequest request = new PlayHistoryRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setProgress(3600);
        request.setDuration(7200);

        Long userId = 7L;

        // Mock数据库查询
        when(playHistoryMapper.findByUserAndContent(userId, 1L, "movie", 1)).thenReturn(null);
        when(playHistoryMapper.insert(any(PlayHistory.class))).thenReturn(1);

        // 执行测试
        PlayHistory result = playService.recordPlayHistory(userId, request);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getEpisode()); // 默认集数为1
        
        // 验证方法调用
        verify(playHistoryMapper, times(1)).findByUserAndContent(userId, 1L, "movie", 1);
    }
}

package com.lisong.controller;

import com.lisong.entity.Comment;
import com.lisong.entity.CommentResponse;
import com.lisong.entity.PaginationData;
import com.lisong.dto.CommentRequest;
import com.lisong.dto.CommentListResponse;
import com.lisong.service.CommentService;
import com.lisong.util.JwtUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 评论控制器测试
 */
@ExtendWith(MockitoExtension.class)
public class CommentControllerTest {

    @Mock
    private CommentService commentService;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private CommentController commentController;

    @Test
    public void testGetComments() {
        // 准备测试数据
        Comment comment1 = new Comment();
        comment1.setId(1L);
        comment1.setUserId(2L);
        comment1.setContentId(1L);
        comment1.setContentType("movie");
        comment1.setContent("这部电影太棒了！");
        comment1.setRating(5);
        comment1.setLikes(23);
        comment1.setUsername("user001");
        comment1.setAvatar("https://example.com/avatar1.jpg");
        comment1.setCreatedAt(LocalDateTime.now());

        Comment comment2 = new Comment();
        comment2.setId(2L);
        comment2.setUserId(3L);
        comment2.setContentId(1L);
        comment2.setContentType("movie");
        comment2.setContent("卡梅隆不愧是大师！");
        comment2.setRating(5);
        comment2.setLikes(18);
        comment2.setUsername("user002");
        comment2.setAvatar("https://example.com/avatar2.jpg");
        comment2.setCreatedAt(LocalDateTime.now());

        List<Comment> comments = Arrays.asList(comment1, comment2);

        PaginationData pagination = new PaginationData();
        pagination.setPage(1);
        pagination.setLimit(10);
        pagination.setTotal(2L);
        pagination.setTotalPages(1);

        CommentListResponse response = new CommentListResponse();
        response.setComments(comments);
        response.setPagination(pagination);

        // Mock服务方法
        when(commentService.getComments("movie", 1L, 1, 10, "latest")).thenReturn(response);

        // 执行测试
        var result = commentController.getComments("movie", 1L, 1, 10, "latest");

        // 验证结果
        assertEquals(200, result.getCode());
        assertEquals("success", result.getMessage());
        assertEquals(2, result.getData().getComments().size());
        assertEquals(1, result.getData().getPagination().getPage());
        assertEquals(2L, result.getData().getPagination().getTotal());
    }

    @Test
    public void testGetCommentsWithInvalidType() {
        // Mock服务方法抛出异常
        when(commentService.getComments("invalid", 1L, 1, 10, "latest"))
                .thenThrow(new IllegalArgumentException("不支持的内容类型: invalid"));

        // 执行测试
        var result = commentController.getComments("invalid", 1L, 1, 10, "latest");

        // 验证结果
        assertEquals(400, result.getCode());
        assertEquals("不支持的内容类型: invalid", result.getMessage());
    }

    @Test
    public void testPostCommentSuccess() {
        // 准备测试数据
        CommentRequest request = new CommentRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setContent("这部电影真的很棒！");
        request.setRating(5);

        CommentResponse commentResponse = new CommentResponse();
        commentResponse.setCommentId(35L);
        commentResponse.setContent("这部电影真的很棒！");
        commentResponse.setRating(5);
        commentResponse.setCreatedAt(LocalDateTime.now());

        String token = "valid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + token);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn(7L);

        // Mock服务方法
        when(commentService.postComment(eq(7L), any(CommentRequest.class))).thenReturn(commentResponse);

        // 执行测试
        var result = commentController.postComment(request, httpRequest);

        // 验证结果
        assertEquals(200, result.getCode());
        assertEquals("评论成功", result.getMessage());
        assertEquals(35L, result.getData().getCommentId());
        assertEquals("这部电影真的很棒！", result.getData().getContent());
        assertEquals(5, result.getData().getRating());
    }

    @Test
    public void testPostCommentWithoutToken() {
        // 准备测试数据
        CommentRequest request = new CommentRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setContent("测试评论");

        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        // 没有Authorization头

        // 执行测试
        var result = commentController.postComment(request, httpRequest);

        // 验证结果
        assertEquals(401, result.getCode());
        assertEquals("未提供有效的认证token", result.getMessage());
    }

    @Test
    public void testPostCommentWithInvalidToken() {
        // 准备测试数据
        CommentRequest request = new CommentRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setContent("测试评论");

        String invalidToken = "invalid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + invalidToken);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(invalidToken)).thenReturn(false);

        // 执行测试
        var result = commentController.postComment(request, httpRequest);

        // 验证结果
        assertEquals(401, result.getCode());
        assertEquals("无效的认证token", result.getMessage());
    }

    @Test
    public void testLikeCommentSuccess() {
        String token = "valid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + token);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn(7L);

        // Mock服务方法
        when(commentService.likeComment(1L, 7L)).thenReturn(true);

        // 执行测试
        var result = commentController.likeComment(1L, httpRequest);

        // 验证结果
        assertEquals(200, result.getCode());
        assertEquals("点赞成功", result.getMessage());
    }

    @Test
    public void testDeleteCommentSuccess() {
        String token = "valid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + token);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn(7L);

        // Mock服务方法
        when(commentService.deleteComment(1L, 7L)).thenReturn(true);

        // 执行测试
        var result = commentController.deleteComment(1L, httpRequest);

        // 验证结果
        assertEquals(200, result.getCode());
        assertEquals("删除成功", result.getMessage());
    }

    @Test
    public void testDeleteCommentNotOwner() {
        String token = "valid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + token);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn(7L);

        // Mock服务方法抛出异常
        when(commentService.deleteComment(1L, 7L))
                .thenThrow(new RuntimeException("只能删除自己的评论"));

        // 执行测试
        var result = commentController.deleteComment(1L, httpRequest);

        // 验证结果
        assertEquals(404, result.getCode());
        assertEquals("只能删除自己的评论", result.getMessage());
    }
}

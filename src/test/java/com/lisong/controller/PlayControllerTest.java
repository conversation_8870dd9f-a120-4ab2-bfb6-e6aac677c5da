package com.lisong.controller;

import com.lisong.entity.PlayResponse;
import com.lisong.entity.PlayHistory;
import com.lisong.entity.Subtitle;
import com.lisong.dto.PlayHistoryRequest;
import com.lisong.service.PlayService;
import com.lisong.util.JwtUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletRequest;

import java.time.LocalDateTime;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 播放控制器测试
 */
@ExtendWith(MockitoExtension.class)
public class PlayControllerTest {

    @Mock
    private PlayService playService;

    @Mock
    private JwtUtil jwtUtil;

    @InjectMocks
    private PlayController playController;

    @Test
    public void testGetMoviePlayUrl() {
        // 准备测试数据
        PlayResponse playResponse = new PlayResponse();
        playResponse.setPlayUrl("https://example.com/play/movie/1/episode_1_1080p.m3u8");
        playResponse.setQuality("1080p");
        playResponse.setDuration(7200);
        playResponse.setSubtitles(Arrays.asList(
            new Subtitle("zh-CN", "https://example.com/subtitles/movie/1/zh.vtt"),
            new Subtitle("en", "https://example.com/subtitles/movie/1/en.vtt")
        ));

        // Mock服务方法
        when(playService.getPlayUrl("movie", 1L, null, null)).thenReturn(playResponse);

        // 执行测试
        var response = playController.getPlayUrl("movie", 1L, null, null);

        // 验证结果
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMessage());
        assertEquals("https://example.com/play/movie/1/episode_1_1080p.m3u8", response.getData().getPlayUrl());
        assertEquals("1080p", response.getData().getQuality());
        assertEquals(7200, response.getData().getDuration());
        assertEquals(2, response.getData().getSubtitles().size());
    }

    @Test
    public void testGetTvSeriesPlayUrl() {
        // 准备测试数据
        PlayResponse playResponse = new PlayResponse();
        playResponse.setPlayUrl("https://example.com/play/tv/1/episode_2_720p.m3u8");
        playResponse.setQuality("720p");
        playResponse.setDuration(2700);
        playResponse.setSubtitles(Arrays.asList(
            new Subtitle("zh-CN", "https://example.com/subtitles/tv/1/zh.vtt"),
            new Subtitle("en", "https://example.com/subtitles/tv/1/en.vtt")
        ));

        // Mock服务方法
        when(playService.getPlayUrl("tv", 1L, 2, "720p")).thenReturn(playResponse);

        // 执行测试
        var response = playController.getPlayUrl("tv", 1L, 2, "720p");

        // 验证结果
        assertEquals(200, response.getCode());
        assertEquals("https://example.com/play/tv/1/episode_2_720p.m3u8", response.getData().getPlayUrl());
        assertEquals("720p", response.getData().getQuality());
    }

    @Test
    public void testGetPlayUrlWithInvalidType() {
        // Mock服务方法抛出异常
        when(playService.getPlayUrl("invalid", 1L, null, null))
                .thenThrow(new IllegalArgumentException("不支持的内容类型: invalid"));

        // 执行测试
        var response = playController.getPlayUrl("invalid", 1L, null, null);

        // 验证结果
        assertEquals(400, response.getCode());
        assertEquals("不支持的内容类型: invalid", response.getMessage());
    }

    @Test
    public void testGetPlayUrlWithNotFound() {
        // Mock服务方法抛出异常
        when(playService.getPlayUrl("movie", 999L, null, null))
                .thenThrow(new RuntimeException("电影不存在"));

        // 执行测试
        var response = playController.getPlayUrl("movie", 999L, null, null);

        // 验证结果
        assertEquals(404, response.getCode());
        assertEquals("电影不存在", response.getMessage());
    }

    @Test
    public void testRecordPlayHistorySuccess() {
        // 准备测试数据
        PlayHistoryRequest request = new PlayHistoryRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setEpisode(1);
        request.setProgress(3600);
        request.setDuration(7200);

        PlayHistory playHistory = new PlayHistory();
        playHistory.setId(1L);
        playHistory.setUserId(7L);
        playHistory.setContentId(1L);
        playHistory.setContentType("movie");
        playHistory.setEpisode(1);
        playHistory.setProgress(3600);
        playHistory.setDuration(7200);
        playHistory.setQuality("1080p");
        playHistory.setWatchedAt(LocalDateTime.now());

        String token = "valid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + token);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(token)).thenReturn(true);
        when(jwtUtil.getUserIdFromToken(token)).thenReturn(7L);

        // Mock服务方法
        when(playService.recordPlayHistory(eq(7L), any(PlayHistoryRequest.class))).thenReturn(playHistory);

        // 执行测试
        var response = playController.recordPlayHistory(request, httpRequest);

        // 验证结果
        assertEquals(200, response.getCode());
        assertEquals(7L, response.getData().getUserId());
        assertEquals(1L, response.getData().getContentId());
        assertEquals("movie", response.getData().getContentType());
        assertEquals(3600, response.getData().getProgress());
    }

    @Test
    public void testRecordPlayHistoryWithoutToken() {
        // 准备测试数据
        PlayHistoryRequest request = new PlayHistoryRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setEpisode(1);
        request.setProgress(3600);
        request.setDuration(7200);

        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        // 没有Authorization头

        // 执行测试
        var response = playController.recordPlayHistory(request, httpRequest);

        // 验证结果
        assertEquals(401, response.getCode());
        assertEquals("未提供有效的认证token", response.getMessage());
    }

    @Test
    public void testRecordPlayHistoryWithInvalidToken() {
        // 准备测试数据
        PlayHistoryRequest request = new PlayHistoryRequest();
        request.setContentId(1L);
        request.setContentType("movie");
        request.setEpisode(1);
        request.setProgress(3600);
        request.setDuration(7200);

        String invalidToken = "invalid-jwt-token";
        MockHttpServletRequest httpRequest = new MockHttpServletRequest();
        httpRequest.addHeader("Authorization", "Bearer " + invalidToken);

        // Mock JWT工具方法
        when(jwtUtil.validateToken(invalidToken)).thenReturn(false);

        // 执行测试
        var response = playController.recordPlayHistory(request, httpRequest);

        // 验证结果
        assertEquals(401, response.getCode());
        assertEquals("无效的认证token", response.getMessage());
    }
}

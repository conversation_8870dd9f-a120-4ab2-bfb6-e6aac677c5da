package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

/**
 * 播放历史实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlayHistory {
    
    private Long id;
    private Long userId;
    private Long contentId;
    private String contentType; // movie, tv, variety
    private Integer episode; // 集数，电影默认为1
    private Integer progress; // 播放进度（秒）
    private Integer duration; // 总时长（秒）
    private String quality; // 播放清晰度
    private LocalDateTime watchedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}

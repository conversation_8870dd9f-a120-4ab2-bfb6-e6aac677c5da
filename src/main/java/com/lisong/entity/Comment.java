package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Comment {
    
    private Long id;
    private Long userId;
    private Long contentId;
    private String contentType; // movie, tv, variety
    private Long parentId; // 父评论ID，用于回复
    private String content; // 评论内容
    private Integer rating; // 评分（1-5），只有主评论才有评分
    private Integer likes; // 点赞数
    private Integer status; // 状态：1-正常，0-删除
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 用户信息（关联查询时使用）
    private String username;
    private String avatar;
    
    // 回复列表（用于嵌套评论）
    private List<Comment> replies;
}

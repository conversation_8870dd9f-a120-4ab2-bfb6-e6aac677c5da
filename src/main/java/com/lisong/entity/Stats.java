package com.lisong.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 * 统计数据实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Stats {

    private Long totalMovies;
    private Long totalTVShows;
    private Long totalVarietyShows;
    private Long totalUsers;
    private Long totalViews;
    private Long monthlyNewContent;
    private BigDecimal averageRating;
    private List<TopGenre> topGenres;
}

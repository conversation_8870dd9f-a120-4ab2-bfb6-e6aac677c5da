package com.lisong.service;

import com.lisong.entity.Movie;
import com.lisong.entity.MovieListData;
import com.lisong.entity.CategoryData;
import java.util.List;

/**
 * 电影服务接口
 */
public interface MovieService {

    /**
     * 获取最新电影列表
     * @param page 页码
     * @param limit 每页数量
     * @param genre 类型筛选
     * @param year 年份筛选
     * @param region 地区筛选
     * @param sort 排序方式
     * @return 电影列表数据
     */
    MovieListData getLatestMovies(Integer page, Integer limit, String genre, Integer year, String region, String sort);

    /**
     * 获取电影分类
     * @return 分类数据
     */
    CategoryData getMovieCategories();

    /**
     * 获取电影详情
     * @param id 电影ID
     * @return 电影详情
     */
    Movie getMovieDetail(Long id);

    // ========== 电影管理功能 ==========

    /**
     * 获取所有电影列表（管理用）
     * @param page 页码
     * @param limit 每页数量
     * @return 电影列表数据
     */
    MovieListData getAllMovies(Integer page, Integer limit);

    /**
     * 创建新电影
     * @param movie 电影信息
     * @return 创建的电影
     */
    Movie createMovie(Movie movie);

    /**
     * 更新电影信息
     * @param id 电影ID
     * @param movie 更新的电影信息
     * @return 更新后的电影
     */
    Movie updateMovie(Long id, Movie movie);

    /**
     * 删除电影
     * @param id 电影ID
     * @return 是否删除成功
     */
    boolean deleteMovie(Long id);
}

package com.lisong.service;

import com.lisong.entity.PlayResponse;
import com.lisong.entity.PlayHistory;
import com.lisong.dto.PlayHistoryRequest;

/**
 * 播放服务接口
 */
public interface PlayService {
    
    /**
     * 获取播放链接
     * @param type 内容类型：movie, tv, variety
     * @param id 内容ID
     * @param episode 集数（可选）
     * @param quality 清晰度（可选）
     * @return 播放响应
     */
    PlayResponse getPlayUrl(String type, Long id, Integer episode, String quality);
    
    /**
     * 记录播放历史
     * @param userId 用户ID
     * @param request 播放历史请求
     * @return 播放历史记录
     */
    PlayHistory recordPlayHistory(Long userId, PlayHistoryRequest request);
}

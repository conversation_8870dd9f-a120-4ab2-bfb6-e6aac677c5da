package com.lisong.service;

import com.lisong.entity.HomeData;
import com.lisong.entity.Carousel;
import java.util.List;

/**
 * 首页服务接口
 */
public interface HomeService {

    /**
     * 获取首页数据
     * @return 首页数据
     */
    HomeData getHomeData();

    // ========== 轮播图管理 ==========

    /**
     * 获取所有轮播图列表
     * @return 轮播图列表
     */
    List<Carousel> getAllCarousels();

    /**
     * 根据ID获取轮播图详情
     * @param id 轮播图ID
     * @return 轮播图详情
     */
    Carousel getCarouselById(Long id);

    /**
     * 创建新轮播图
     * @param carousel 轮播图信息
     * @return 创建的轮播图
     */
    Carousel createCarousel(Carousel carousel);

    /**
     * 更新轮播图
     * @param id 轮播图ID
     * @param carousel 更新的轮播图信息
     * @return 更新后的轮播图
     */
    Carousel updateCarousel(Long id, Carousel carousel);

    /**
     * 删除轮播图
     * @param id 轮播图ID
     * @return 是否删除成功
     */
    boolean deleteCarousel(Long id);
}

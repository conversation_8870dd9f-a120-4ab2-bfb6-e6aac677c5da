package com.lisong.service;

import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;
import java.util.List;

/**
 * 电视剧服务接口
 */
public interface TvSeriesService {

    /**
     * 获取电视剧列表
     * @param page 页码
     * @param limit 每页数量
     * @param genre 类型筛选
     * @param region 地区筛选
     * @param status 状态筛选
     * @param sort 排序方式
     * @return 电视剧列表数据
     */
    TvSeriesListData getTvSeriesList(Integer page, Integer limit, String genre, String region, String status, String sort);

    /**
     * 获取电视剧详情
     * @param id 电视剧ID
     * @return 电视剧详情
     */
    TvSeries getTvSeriesDetail(Long id);

    // ========== 电视剧管理功能 ==========

    /**
     * 获取所有电视剧列表（管理用）
     * @param page 页码
     * @param limit 每页数量
     * @return 电视剧列表数据
     */
    TvSeriesListData getAllTvSeries(Integer page, Integer limit);

    /**
     * 创建新电视剧
     * @param tvSeries 电视剧信息
     * @return 创建的电视剧
     */
    TvSeries createTvSeries(TvSeries tvSeries);

    /**
     * 更新电视剧信息
     * @param id 电视剧ID
     * @param tvSeries 更新的电视剧信息
     * @return 更新后的电视剧
     */
    TvSeries updateTvSeries(Long id, TvSeries tvSeries);

    /**
     * 删除电视剧
     * @param id 电视剧ID
     * @return 是否删除成功
     */
    boolean deleteTvSeries(Long id);
}

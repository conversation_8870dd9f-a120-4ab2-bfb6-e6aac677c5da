package com.lisong.service;

import com.lisong.entity.Comment;
import com.lisong.entity.CommentResponse;
import com.lisong.dto.CommentRequest;
import com.lisong.dto.CommentListResponse;

/**
 * 评论服务接口
 */
public interface CommentService {
    
    /**
     * 获取评论列表
     * @param type 内容类型：movie, tv, variety
     * @param id 内容ID
     * @param page 页码
     * @param limit 每页数量
     * @param sort 排序方式：latest, hottest
     * @return 评论列表响应
     */
    CommentListResponse getComments(String type, Long id, Integer page, Integer limit, String sort);
    
    /**
     * 发表评论
     * @param userId 用户ID
     * @param request 评论请求
     * @return 评论响应
     */
    CommentResponse postComment(Long userId, CommentRequest request);
    
    /**
     * 点赞评论
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean likeComment(Long commentId, Long userId);
    
    /**
     * 删除评论
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean deleteComment(Long commentId, Long userId);
}

package com.lisong.service.impl;

import com.lisong.entity.HomeData;
import com.lisong.entity.Carousel;
import com.lisong.entity.Movie;
import com.lisong.entity.Stats;
import com.lisong.mapper.CarouselMapper;
import com.lisong.mapper.MovieMapper;
import com.lisong.mapper.StatsMapper;
import com.lisong.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 首页服务实现
 */
@Service
public class HomeServiceImpl implements HomeService {

    @Autowired
    private CarouselMapper carouselMapper;

    @Autowired
    private MovieMapper movieMapper;

    @Autowired
    private StatsMapper statsMapper;

    @Override
    public HomeData getHomeData() {
        // 获取轮播图数据
        List<Carousel> carousels = carouselMapper.findActiveCarousels();

        // 获取热门电影
        List<Movie> hotMovies = movieMapper.findHotMovies();

        // 获取最新电影
        List<Movie> latestMovies = movieMapper.findLatestMovies();

        // 获取统计数据
        Stats stats = new Stats();
        stats.setTotalMovies(statsMapper.countMovies());
        stats.setTotalTVShows(statsMapper.countTVShows());
        stats.setTotalVarietyShows(statsMapper.countVarietyShows());
        stats.setTotalUsers(statsMapper.countUsers());

        // 封装首页数据
        HomeData homeData = new HomeData();
        homeData.setCarousel(carousels);
        homeData.setHotMovies(hotMovies);
        homeData.setLatestMovies(latestMovies);
        homeData.setStats(stats);

        return homeData;
    }

    // ========== 轮播图管理实现 ==========

    @Override
    public List<Carousel> getAllCarousels() {
        return carouselMapper.findAll();
    }

    @Override
    public Carousel getCarouselById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("轮播图ID不能为空");
        }
        return carouselMapper.findById(id);
    }

    @Override
    public Carousel createCarousel(Carousel carousel) {
        // 参数验证
        if (carousel == null) {
            throw new IllegalArgumentException("轮播图信息不能为空");
        }
        if (carousel.getTitle() == null || carousel.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("轮播图标题不能为空");
        }
        if (carousel.getImage() == null || carousel.getImage().trim().isEmpty()) {
            throw new IllegalArgumentException("轮播图图片不能为空");
        }

        // 设置默认值
        if (carousel.getSortOrder() == null) {
            carousel.setSortOrder(0);
        }
        if (carousel.getStatus() == null) {
            carousel.setStatus(1); // 默认启用
        }

        // 设置时间戳
        carousel.setCreatedAt(LocalDateTime.now());
        carousel.setUpdatedAt(LocalDateTime.now());

        // 插入数据库
        int result = carouselMapper.insert(carousel);
        if (result > 0) {
            return carousel;
        } else {
            throw new RuntimeException("创建轮播图失败");
        }
    }

    @Override
    public Carousel updateCarousel(Long id, Carousel carousel) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("轮播图ID不能为空");
        }
        if (carousel == null) {
            throw new IllegalArgumentException("轮播图信息不能为空");
        }

        // 检查轮播图是否存在
        Carousel existingCarousel = carouselMapper.findById(id);
        if (existingCarousel == null) {
            throw new RuntimeException("轮播图不存在");
        }

        // 更新字段
        carousel.setId(id);
        if (carousel.getTitle() == null || carousel.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("轮播图标题不能为空");
        }
        if (carousel.getImage() == null || carousel.getImage().trim().isEmpty()) {
            throw new IllegalArgumentException("轮播图图片不能为空");
        }
        if (carousel.getSortOrder() == null) {
            carousel.setSortOrder(existingCarousel.getSortOrder());
        }
        if (carousel.getStatus() == null) {
            carousel.setStatus(existingCarousel.getStatus());
        }

        // 设置更新时间
        carousel.setUpdatedAt(LocalDateTime.now());

        // 更新数据库
        int result = carouselMapper.update(carousel);
        if (result > 0) {
            return carouselMapper.findById(id);
        } else {
            throw new RuntimeException("更新轮播图失败");
        }
    }

    @Override
    public boolean deleteCarousel(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("轮播图ID不能为空");
        }

        // 检查轮播图是否存在
        Carousel existingCarousel = carouselMapper.findById(id);
        if (existingCarousel == null) {
            throw new RuntimeException("轮播图不存在");
        }

        // 软删除
        int result = carouselMapper.deleteById(id);
        return result > 0;
    }
}

package com.lisong.service.impl;

import com.lisong.entity.*;
import com.lisong.dto.PlayHistoryRequest;
import com.lisong.mapper.*;
import com.lisong.service.PlayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 播放服务实现
 */
@Service
public class PlayServiceImpl implements PlayService {
    
    @Autowired
    private MovieMapper movieMapper;
    
    @Autowired
    private TvSeriesMapper tvSeriesMapper;
    
    @Autowired
    private TvSeriesEpisodeMapper tvSeriesEpisodeMapper;
    
    @Autowired
    private VarietyShowMapper varietyShowMapper;
    
    @Autowired
    private VarietyShowEpisodeMapper varietyShowEpisodeMapper;
    
    @Autowired
    private PlayHistoryMapper playHistoryMapper;
    
    @Override
    public PlayResponse getPlayUrl(String type, Long id, Integer episode, String quality) {
        PlayResponse response = new PlayResponse();
        
        // 设置默认清晰度
        if (quality == null || quality.isEmpty()) {
            quality = "1080p";
        }
        response.setQuality(quality);
        
        // 设置默认字幕
        List<Subtitle> subtitles = Arrays.asList(
            new Subtitle("zh-CN", "https://example.com/subtitles/" + type + "/" + id + "/zh.vtt"),
            new Subtitle("en", "https://example.com/subtitles/" + type + "/" + id + "/en.vtt")
        );
        response.setSubtitles(subtitles);
        
        switch (type.toLowerCase()) {
            case "movie":
                return getMoviePlayUrl(id, quality, response);
            case "tv":
                return getTvSeriesPlayUrl(id, episode, quality, response);
            case "variety":
                return getVarietyShowPlayUrl(id, episode, quality, response);
            default:
                throw new IllegalArgumentException("不支持的内容类型: " + type);
        }
    }
    
    private PlayResponse getMoviePlayUrl(Long id, String quality, PlayResponse response) {
        Movie movie = movieMapper.findById(id);
        if (movie == null) {
            throw new RuntimeException("电影不存在");
        }
        
        // 生成播放链接（实际项目中应该从CDN或视频服务获取）
        String playUrl = generatePlayUrl("movie", id, 1, quality);
        response.setPlayUrl(playUrl);
        response.setDuration(movie.getDuration() * 60); // 转换为秒
        
        return response;
    }
    
    private PlayResponse getTvSeriesPlayUrl(Long id, Integer episode, String quality, PlayResponse response) {
        if (episode == null) {
            episode = 1;
        }
        
        TvSeries tvSeries = tvSeriesMapper.findById(id);
        if (tvSeries == null) {
            throw new RuntimeException("电视剧不存在");
        }
        
        TvSeriesEpisode episodeInfo = tvSeriesEpisodeMapper.findBySeriesIdAndEpisode(id, episode);
        if (episodeInfo == null) {
            throw new RuntimeException("剧集不存在");
        }
        
        String playUrl = generatePlayUrl("tv", id, episode, quality);
        response.setPlayUrl(playUrl);
        response.setDuration(episodeInfo.getDuration() * 60); // 转换为秒
        
        return response;
    }
    
    private PlayResponse getVarietyShowPlayUrl(Long id, Integer episode, String quality, PlayResponse response) {
        if (episode == null) {
            episode = 1;
        }
        
        VarietyShow varietyShow = varietyShowMapper.findById(id);
        if (varietyShow == null) {
            throw new RuntimeException("综艺节目不存在");
        }
        
        VarietyShowEpisode episodeInfo = varietyShowEpisodeMapper.findByShowIdAndEpisode(id, episode);
        if (episodeInfo == null) {
            throw new RuntimeException("节目期数不存在");
        }
        
        String playUrl = generatePlayUrl("variety", id, episode, quality);
        response.setPlayUrl(playUrl);
        response.setDuration(episodeInfo.getDuration() * 60); // 转换为秒
        
        return response;
    }
    
    /**
     * 生成播放链接（模拟）
     */
    private String generatePlayUrl(String type, Long id, Integer episode, String quality) {
        return String.format("https://example.com/play/%s/%d/episode_%d_%s.m3u8", 
                           type, id, episode, quality);
    }
    
    @Override
    public PlayHistory recordPlayHistory(Long userId, PlayHistoryRequest request) {
        // 设置默认值
        if (request.getEpisode() == null) {
            request.setEpisode(1);
        }
        
        // 查找是否已存在播放记录
        PlayHistory existingHistory = playHistoryMapper.findByUserAndContent(
            userId, request.getContentId(), request.getContentType(), request.getEpisode());
        
        PlayHistory playHistory;
        if (existingHistory != null) {
            // 更新现有记录
            existingHistory.setProgress(request.getProgress());
            existingHistory.setDuration(request.getDuration());
            existingHistory.setWatchedAt(LocalDateTime.now());
            playHistoryMapper.update(existingHistory);
            playHistory = existingHistory;
        } else {
            // 创建新记录
            playHistory = new PlayHistory();
            playHistory.setUserId(userId);
            playHistory.setContentId(request.getContentId());
            playHistory.setContentType(request.getContentType());
            playHistory.setEpisode(request.getEpisode());
            playHistory.setProgress(request.getProgress());
            playHistory.setDuration(request.getDuration());
            playHistory.setQuality("1080p"); // 默认清晰度
            playHistory.setWatchedAt(LocalDateTime.now());
            playHistoryMapper.insert(playHistory);
        }
        
        return playHistory;
    }
}

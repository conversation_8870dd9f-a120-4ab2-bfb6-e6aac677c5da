package com.lisong.service.impl;

import com.lisong.entity.Stats;
import com.lisong.entity.TopGenre;
import com.lisong.mapper.StatsMapper;
import com.lisong.service.StatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 统计服务实现类
 */
@Service
public class StatsServiceImpl implements StatsService {
    
    @Autowired
    private StatsMapper statsMapper;
    
    @Override
    public Stats getWebsiteStats() {
        Stats stats = new Stats();
        
        // 获取基础统计数据
        stats.setTotalMovies(statsMapper.countMovies());
        stats.setTotalTVShows(statsMapper.countTVShows());
        stats.setTotalVarietyShows(statsMapper.countVarietyShows());
        stats.setTotalUsers(statsMapper.countUsers());
        
        // 获取总观看数
        stats.setTotalViews(statsMapper.getTotalViews());
        
        // 获取本月新增内容数量
        stats.setMonthlyNewContent(statsMapper.getMonthlyNewContent());
        
        // 获取平均评分
        BigDecimal avgRating = statsMapper.getAverageRating();
        stats.setAverageRating(avgRating != null ? avgRating : BigDecimal.ZERO);
        
        // 获取热门类型
        List<TopGenre> topGenres = statsMapper.getTopGenres();
        stats.setTopGenres(topGenres);
        
        return stats;
    }
}

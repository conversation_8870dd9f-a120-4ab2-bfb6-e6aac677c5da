package com.lisong.service.impl;

import com.lisong.entity.*;
import com.lisong.mapper.MovieMapper;
import com.lisong.mapper.MovieCategoryMapper;
import com.lisong.service.MovieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电影服务实现
 */
@Service
public class MovieServiceImpl implements MovieService {
    
    @Autowired
    private MovieMapper movieMapper;
    
    @Autowired
    private MovieCategoryMapper movieCategoryMapper;
    
    @Override
    public MovieListData getLatestMovies(Integer page, Integer limit, String genre, Integer year, String region, String sort) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }
        
        // 计算偏移量
        Integer offset = (page - 1) * limit;
        
        // 查询电影列表
        List<Movie> movies = movieMapper.findLatestMoviesWithFilter(genre, year, region, sort, offset, limit);
        
        // 查询总数
        Long total = movieMapper.countMoviesWithFilter(genre, year, region);
        
        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);
        
        return new MovieListData(movies, pagination);
    }
    
    @Override
    public CategoryData getMovieCategories() {
        // 获取所有分类
        List<MovieCategory> categories = movieCategoryMapper.findAllActiveCategories();
        
        // 为每个分类统计电影数量
        for (MovieCategory category : categories) {
            Long count = movieCategoryMapper.countMoviesByCategory(category.getName());
            category.setCount(count);
        }
        
        // 获取统计数据
        CategoryStats stats = new CategoryStats();
        stats.setTotalCategories(movieCategoryMapper.countCategories());
        stats.setTotalMovies(movieMapper.countMovies());
        stats.setMonthlyNew(movieMapper.countMonthlyNewMovies());
        
        Double avgRating = movieMapper.getAverageRating();
        stats.setAverageRating(avgRating != null ? BigDecimal.valueOf(avgRating) : BigDecimal.ZERO);
        
        return new CategoryData(categories, stats);
    }
    
    @Override
    public Movie getMovieDetail(Long id) {
        return movieMapper.findById(id);
    }

    // ========== 电影管理功能实现 ==========

    @Override
    public MovieListData getAllMovies(Integer page, Integer limit) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }

        // 计算偏移量
        Integer offset = (page - 1) * limit;

        // 查询电影列表
        List<Movie> movies = movieMapper.findAllWithPagination(offset, limit);

        // 查询总数
        Long total = movieMapper.countAllMovies();

        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);

        return new MovieListData(movies, pagination);
    }

    @Override
    public Movie createMovie(Movie movie) {
        // 参数验证
        if (movie == null) {
            throw new IllegalArgumentException("电影信息不能为空");
        }
        if (movie.getTitle() == null || movie.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("电影标题不能为空");
        }

        // 设置默认值
        if (movie.getRating() == null) {
            movie.setRating(BigDecimal.ZERO);
        }
        if (movie.getMovieStatus() == null) {
            movie.setMovieStatus("released");
        }
        if (movie.getIsHot() == null) {
            movie.setIsHot(0);
        }
        if (movie.getIsLatest() == null) {
            movie.setIsLatest(0);
        }
        if (movie.getViewCount() == null) {
            movie.setViewCount(0L);
        }
        if (movie.getLikeCount() == null) {
            movie.setLikeCount(0L);
        }
        if (movie.getStatus() == null) {
            movie.setStatus(1); // 默认启用
        }

        // 插入数据库
        int result = movieMapper.insert(movie);
        if (result > 0) {
            return movie;
        } else {
            throw new RuntimeException("创建电影失败");
        }
    }

    @Override
    public Movie updateMovie(Long id, Movie movie) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }
        if (movie == null) {
            throw new IllegalArgumentException("电影信息不能为空");
        }

        // 检查电影是否存在
        Movie existingMovie = movieMapper.findById(id);
        if (existingMovie == null) {
            throw new RuntimeException("电影不存在");
        }

        // 更新字段
        movie.setId(id);
        if (movie.getTitle() == null || movie.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("电影标题不能为空");
        }

        // 保留原有值（如果新值为null）
        if (movie.getRating() == null) {
            movie.setRating(existingMovie.getRating());
        }
        if (movie.getMovieStatus() == null) {
            movie.setMovieStatus(existingMovie.getMovieStatus());
        }
        if (movie.getIsHot() == null) {
            movie.setIsHot(existingMovie.getIsHot());
        }
        if (movie.getIsLatest() == null) {
            movie.setIsLatest(existingMovie.getIsLatest());
        }
        if (movie.getViewCount() == null) {
            movie.setViewCount(existingMovie.getViewCount());
        }
        if (movie.getLikeCount() == null) {
            movie.setLikeCount(existingMovie.getLikeCount());
        }
        if (movie.getStatus() == null) {
            movie.setStatus(existingMovie.getStatus());
        }

        // 更新数据库
        int result = movieMapper.update(movie);
        if (result > 0) {
            return movieMapper.findById(id);
        } else {
            throw new RuntimeException("更新电影失败");
        }
    }

    @Override
    public boolean deleteMovie(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("电影ID不能为空");
        }

        // 检查电影是否存在
        Movie existingMovie = movieMapper.findById(id);
        if (existingMovie == null) {
            throw new RuntimeException("电影不存在");
        }

        // 软删除
        int result = movieMapper.deleteById(id);
        return result > 0;
    }
}

package com.lisong.service.impl;

import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;
import com.lisong.entity.TvSeriesEpisode;
import com.lisong.entity.PaginationData;
import com.lisong.mapper.TvSeriesMapper;
import com.lisong.mapper.TvSeriesEpisodeMapper;
import com.lisong.service.TvSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 电视剧服务实现
 */
@Service
public class TvSeriesServiceImpl implements TvSeriesService {
    
    @Autowired
    private TvSeriesMapper tvSeriesMapper;
    
    @Autowired
    private TvSeriesEpisodeMapper tvSeriesEpisodeMapper;
    
    @Override
    public TvSeriesListData getTvSeriesList(Integer page, Integer limit, String genre, String region, String status, String sort) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }
        
        // 计算偏移量
        Integer offset = (page - 1) * limit;
        
        // 查询电视剧列表
        List<TvSeries> tvSeriesList = tvSeriesMapper.findTvSeriesWithFilter(genre, region, status, sort, offset, limit);
        
        // 查询总数
        Long total = tvSeriesMapper.countTvSeriesWithFilter(genre, region, status);
        
        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);
        
        return new TvSeriesListData(tvSeriesList, pagination);
    }
    
    @Override
    public TvSeries getTvSeriesDetail(Long id) {
        // 查询电视剧基本信息
        TvSeries tvSeries = tvSeriesMapper.findById(id);

        if (tvSeries != null) {
            // 查询剧集列表
            List<TvSeriesEpisode> episodes = tvSeriesEpisodeMapper.findBySeriesId(id);
            tvSeries.setEpisodes(episodes);
        }

        return tvSeries;
    }

    // ========== 电视剧管理功能实现 ==========

    @Override
    public TvSeriesListData getAllTvSeries(Integer page, Integer limit) {
        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1) {
            limit = 12;
        }

        // 计算偏移量
        Integer offset = (page - 1) * limit;

        // 查询电视剧列表
        List<TvSeries> tvSeriesList = tvSeriesMapper.findAllWithPagination(offset, limit);

        // 查询总数
        Long total = tvSeriesMapper.countAllTvSeries();

        // 创建分页数据
        PaginationData pagination = new PaginationData(page, limit, total);

        return new TvSeriesListData(tvSeriesList, pagination);
    }

    @Override
    public TvSeries createTvSeries(TvSeries tvSeries) {
        // 参数验证
        if (tvSeries == null) {
            throw new IllegalArgumentException("电视剧信息不能为空");
        }
        if (tvSeries.getTitle() == null || tvSeries.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("电视剧标题不能为空");
        }

        // 设置默认值
        if (tvSeries.getRating() == null) {
            tvSeries.setRating(BigDecimal.ZERO);
        }
        if (tvSeries.getSeriesStatus() == null) {
            tvSeries.setSeriesStatus("ongoing");
        }
        if (tvSeries.getCurrentEpisode() == null) {
            tvSeries.setCurrentEpisode(1);
        }
        if (tvSeries.getViewCount() == null) {
            tvSeries.setViewCount(0L);
        }
        if (tvSeries.getLikeCount() == null) {
            tvSeries.setLikeCount(0L);
        }
        if (tvSeries.getStatus() == null) {
            tvSeries.setStatus(1); // 默认启用
        }

        // 插入数据库
        int result = tvSeriesMapper.insert(tvSeries);
        if (result > 0) {
            return tvSeries;
        } else {
            throw new RuntimeException("创建电视剧失败");
        }
    }

    @Override
    public TvSeries updateTvSeries(Long id, TvSeries tvSeries) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("电视剧ID不能为空");
        }
        if (tvSeries == null) {
            throw new IllegalArgumentException("电视剧信息不能为空");
        }

        // 检查电视剧是否存在
        TvSeries existingTvSeries = tvSeriesMapper.findByIdForAdmin(id);
        if (existingTvSeries == null) {
            throw new RuntimeException("电视剧不存在");
        }

        // 更新字段
        tvSeries.setId(id);
        if (tvSeries.getTitle() == null || tvSeries.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("电视剧标题不能为空");
        }

        // 保留原有值（如果新值为null）
        if (tvSeries.getRating() == null) {
            tvSeries.setRating(existingTvSeries.getRating());
        }
        if (tvSeries.getSeriesStatus() == null) {
            tvSeries.setSeriesStatus(existingTvSeries.getSeriesStatus());
        }
        if (tvSeries.getCurrentEpisode() == null) {
            tvSeries.setCurrentEpisode(existingTvSeries.getCurrentEpisode());
        }
        if (tvSeries.getTotalEpisodes() == null) {
            tvSeries.setTotalEpisodes(existingTvSeries.getTotalEpisodes());
        }
        if (tvSeries.getViewCount() == null) {
            tvSeries.setViewCount(existingTvSeries.getViewCount());
        }
        if (tvSeries.getLikeCount() == null) {
            tvSeries.setLikeCount(existingTvSeries.getLikeCount());
        }
        if (tvSeries.getStatus() == null) {
            tvSeries.setStatus(existingTvSeries.getStatus());
        }

        // 更新数据库
        int result = tvSeriesMapper.update(tvSeries);
        if (result > 0) {
            return tvSeriesMapper.findByIdForAdmin(id);
        } else {
            throw new RuntimeException("更新电视剧失败");
        }
    }

    @Override
    public boolean deleteTvSeries(Long id) {
        // 参数验证
        if (id == null) {
            throw new IllegalArgumentException("电视剧ID不能为空");
        }

        // 检查电视剧是否存在
        TvSeries existingTvSeries = tvSeriesMapper.findByIdForAdmin(id);
        if (existingTvSeries == null) {
            throw new RuntimeException("电视剧不存在");
        }

        // 软删除
        int result = tvSeriesMapper.deleteById(id);
        return result > 0;
    }
}

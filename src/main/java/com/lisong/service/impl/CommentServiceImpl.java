package com.lisong.service.impl;

import com.lisong.entity.Comment;
import com.lisong.entity.CommentResponse;
import com.lisong.entity.PaginationData;
import com.lisong.dto.CommentRequest;
import com.lisong.dto.CommentListResponse;
import com.lisong.mapper.CommentMapper;
import com.lisong.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论服务实现
 */
@Service
public class CommentServiceImpl implements CommentService {
    
    @Autowired
    private CommentMapper commentMapper;
    
    @Override
    public CommentListResponse getComments(String type, Long id, Integer page, Integer limit, String sort) {
        // 参数验证和默认值设置
        if (page == null || page < 1) {
            page = 1;
        }
        if (limit == null || limit < 1 || limit > 50) {
            limit = 10;
        }
        if (sort == null || sort.isEmpty()) {
            sort = "latest";
        }
        
        // 验证内容类型
        if (!isValidContentType(type)) {
            throw new IllegalArgumentException("不支持的内容类型: " + type);
        }
        
        // 确定排序方式
        String orderBy;
        switch (sort.toLowerCase()) {
            case "hottest":
                orderBy = "c.likes DESC, c.created_at DESC";
                break;
            case "latest":
            default:
                orderBy = "c.created_at DESC";
                break;
        }
        
        // 计算偏移量
        int offset = (page - 1) * limit;
        
        // 获取主评论列表
        List<Comment> mainComments = commentMapper.findMainCommentsByContent(id, type, orderBy, offset, limit);
        
        // 为每个主评论获取回复
        for (Comment comment : mainComments) {
            List<Comment> replies = commentMapper.findRepliesByParentId(comment.getId());
            comment.setReplies(replies);
        }
        
        // 获取总数
        int total = commentMapper.countMainCommentsByContent(id, type);
        int totalPages = (int) Math.ceil((double) total / limit);
        
        // 构建分页信息
        PaginationData pagination = new PaginationData();
        pagination.setPage(page);
        pagination.setLimit(limit);
        pagination.setTotal((long) total);
        pagination.setTotalPages(totalPages);
        
        // 构建响应
        CommentListResponse response = new CommentListResponse();
        response.setComments(mainComments);
        response.setPagination(pagination);
        
        return response;
    }
    
    @Override
    public CommentResponse postComment(Long userId, CommentRequest request) {
        // 参数验证
        if (request.getContentId() == null) {
            throw new IllegalArgumentException("内容ID不能为空");
        }
        if (request.getContent() == null || request.getContent().trim().isEmpty()) {
            throw new IllegalArgumentException("评论内容不能为空");
        }
        if (request.getContent().length() > 1000) {
            throw new IllegalArgumentException("评论内容不能超过1000字符");
        }
        if (!isValidContentType(request.getContentType())) {
            throw new IllegalArgumentException("不支持的内容类型: " + request.getContentType());
        }
        
        // 如果是回复，验证父评论是否存在
        if (request.getParentId() != null) {
            Comment parentComment = commentMapper.findById(request.getParentId());
            if (parentComment == null) {
                throw new RuntimeException("父评论不存在");
            }
            // 回复不能有评分
            request.setRating(null);
        } else {
            // 主评论可以有评分，验证评分范围
            if (request.getRating() != null && (request.getRating() < 1 || request.getRating() > 5)) {
                throw new IllegalArgumentException("评分必须在1-5之间");
            }
        }
        
        // 创建评论对象
        Comment comment = new Comment();
        comment.setUserId(userId);
        comment.setContentId(request.getContentId());
        comment.setContentType(request.getContentType());
        comment.setParentId(request.getParentId());
        comment.setContent(request.getContent().trim());
        comment.setRating(request.getRating());
        comment.setLikes(0);
        comment.setStatus(1);
        comment.setCreatedAt(LocalDateTime.now());
        comment.setUpdatedAt(LocalDateTime.now());
        
        // 插入数据库
        int result = commentMapper.insert(comment);
        if (result <= 0) {
            throw new RuntimeException("评论发表失败");
        }
        
        // 构建响应
        CommentResponse response = new CommentResponse();
        response.setCommentId(comment.getId());
        response.setContent(comment.getContent());
        response.setRating(comment.getRating());
        response.setCreatedAt(comment.getCreatedAt());
        
        return response;
    }
    
    @Override
    public boolean likeComment(Long commentId, Long userId) {
        // 查找评论
        Comment comment = commentMapper.findById(commentId);
        if (comment == null) {
            throw new RuntimeException("评论不存在");
        }
        
        // 增加点赞数（这里简化处理，实际项目中应该记录用户点赞状态避免重复点赞）
        int newLikes = comment.getLikes() + 1;
        int result = commentMapper.updateLikes(commentId, newLikes);
        
        return result > 0;
    }
    
    @Override
    public boolean deleteComment(Long commentId, Long userId) {
        // 查找评论
        Comment comment = commentMapper.findById(commentId);
        if (comment == null) {
            throw new RuntimeException("评论不存在");
        }
        
        // 验证是否是评论作者
        if (!comment.getUserId().equals(userId)) {
            throw new RuntimeException("只能删除自己的评论");
        }
        
        // 软删除评论
        int result = commentMapper.deleteById(commentId);
        
        return result > 0;
    }
    
    /**
     * 验证内容类型是否有效
     */
    private boolean isValidContentType(String type) {
        return "movie".equals(type) || "tv".equals(type) || "variety".equals(type);
    }
}

package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.TvSeries;
import com.lisong.entity.TvSeriesListData;
import com.lisong.service.TvSeriesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 电视剧控制器
 */
@RestController
@RequestMapping("/tv-series")
public class TvSeriesController {
    
    @Autowired
    private TvSeriesService tvSeriesService;
    
    /**
     * 获取电视剧列表
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @param genre 类型筛选：drama,romance,comedy,action,thriller
     * @param region 地区筛选：cn,kr,us,jp,uk
     * @param status 状态筛选：ongoing,completed,upcoming
     * @param sort 排序方式：latest,rating,popular,name
     * @return 电视剧列表响应
     */
    @GetMapping
    public ApiResponse<TvSeriesListData> getTvSeriesList(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "genre", required = false) String genre,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "sort", required = false) String sort) {
        try {
            TvSeriesListData data = tvSeriesService.getTvSeriesList(page, limit, genre, region, status, sort);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取电视剧列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取电视剧详情
     * @param id 电视剧ID
     * @return 电视剧详情响应
     */
    @GetMapping("/{id}")
    public ApiResponse<TvSeries> getTvSeriesDetail(@PathVariable Long id) {
        try {
            TvSeries tvSeries = tvSeriesService.getTvSeriesDetail(id);
            if (tvSeries == null) {
                return ApiResponse.error(404, "电视剧不存在");
            }
            return ApiResponse.success(tvSeries);
        } catch (Exception e) {
            return ApiResponse.error("获取电视剧详情失败: " + e.getMessage());
        }
    }

    // ========== 电视剧管理接口 ==========

    /**
     * 获取所有电视剧列表（管理用）
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @return 电视剧列表响应
     */
    @GetMapping("/admin/all")
    public ApiResponse<TvSeriesListData> getAllTvSeries(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit) {
        try {
            TvSeriesListData data = tvSeriesService.getAllTvSeries(page, limit);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取电视剧列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建新电视剧
     * @param tvSeries 电视剧信息
     * @return 创建的电视剧响应
     */
    @PostMapping
    public ApiResponse<TvSeries> createTvSeries(@RequestBody TvSeries tvSeries) {
        try {
            TvSeries createdTvSeries = tvSeriesService.createTvSeries(tvSeries);
            return ApiResponse.success(createdTvSeries);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(500, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("创建电视剧失败: " + e.getMessage());
        }
    }

    /**
     * 更新电视剧信息
     * @param id 电视剧ID
     * @param tvSeries 更新的电视剧信息
     * @return 更新后的电视剧响应
     */
    @PutMapping("/{id}")
    public ApiResponse<TvSeries> updateTvSeries(@PathVariable Long id, @RequestBody TvSeries tvSeries) {
        try {
            TvSeries updatedTvSeries = tvSeriesService.updateTvSeries(id, tvSeries);
            return ApiResponse.success(updatedTvSeries);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("更新电视剧失败: " + e.getMessage());
        }
    }

    /**
     * 删除电视剧
     * @param id 电视剧ID
     * @return 删除结果响应
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteTvSeries(@PathVariable Long id) {
        try {
            boolean deleted = tvSeriesService.deleteTvSeries(id);
            if (deleted) {
                return ApiResponse.success("电视剧删除成功");
            } else {
                return ApiResponse.error("电视剧删除失败");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("删除电视剧失败: " + e.getMessage());
        }
    }
}

package com.lisong.controller;

import com.lisong.entity.Stats;
import com.lisong.entity.ApiResponse;
import com.lisong.service.StatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统计接口控制器
 */
@RestController
@RequestMapping("/stats")
public class StatsController {
    
    @Autowired
    private StatsService statsService;
    
    /**
     * 获取网站统计数据
     * @return 统计数据响应
     */
    @GetMapping
    public ApiResponse<Stats> getWebsiteStats() {
        try {
            Stats stats = statsService.getWebsiteStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取统计数据失败: " + e.getMessage());
        }
    }
}

package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.CommentResponse;
import com.lisong.dto.CommentRequest;
import com.lisong.dto.CommentListResponse;
import com.lisong.service.CommentService;
import com.lisong.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 评论控制器
 */
@RestController
@RequestMapping("/comments")
public class CommentController {
    
    @Autowired
    private CommentService commentService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取评论列表
     * @param type 内容类型：movie, tv, variety
     * @param id 内容ID
     * @param page 页码，默认1
     * @param limit 每页数量，默认10
     * @param sort 排序：latest, hottest
     * @return 评论列表响应
     */
    @GetMapping("/{type}/{id}")
    public ApiResponse<CommentListResponse> getComments(
            @PathVariable String type,
            @PathVariable Long id,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer limit,
            @RequestParam(required = false) String sort) {
        try {
            CommentListResponse response = commentService.getComments(type, id, page, limit, sort);
            return ApiResponse.success(response);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("获取评论列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 发表评论
     * @param request 评论请求
     * @param httpRequest HTTP请求（用于获取JWT token）
     * @return 评论响应
     */
    @PostMapping
    public ApiResponse<CommentResponse> postComment(
            @RequestBody CommentRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取JWT token
            String authHeader = httpRequest.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error(401, "未提供有效的认证token");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtUtil.validateToken(token)) {
                return ApiResponse.error(401, "无效的认证token");
            }
            
            // 从token中获取用户ID
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return ApiResponse.error(401, "无法获取用户信息");
            }
            
            CommentResponse response = commentService.postComment(userId, request);
            return ApiResponse.success(response, "评论成功");
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("发表评论失败: " + e.getMessage());
        }
    }
    
    /**
     * 点赞评论
     * @param commentId 评论ID
     * @param httpRequest HTTP请求（用于获取JWT token）
     * @return 操作结果
     */
    @PostMapping("/{commentId}/like")
    public ApiResponse<String> likeComment(
            @PathVariable Long commentId,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取JWT token
            String authHeader = httpRequest.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error(401, "未提供有效的认证token");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtUtil.validateToken(token)) {
                return ApiResponse.error(401, "无效的认证token");
            }
            
            // 从token中获取用户ID
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return ApiResponse.error(401, "无法获取用户信息");
            }
            
            boolean success = commentService.likeComment(commentId, userId);
            if (success) {
                return ApiResponse.success("点赞成功");
            } else {
                return ApiResponse.error("点赞失败");
            }
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("点赞失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除评论
     * @param commentId 评论ID
     * @param httpRequest HTTP请求（用于获取JWT token）
     * @return 操作结果
     */
    @DeleteMapping("/{commentId}")
    public ApiResponse<String> deleteComment(
            @PathVariable Long commentId,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取JWT token
            String authHeader = httpRequest.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error(401, "未提供有效的认证token");
            }
            
            String token = authHeader.substring(7);
            
            if (!jwtUtil.validateToken(token)) {
                return ApiResponse.error(401, "无效的认证token");
            }
            
            // 从token中获取用户ID
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return ApiResponse.error(401, "无法获取用户信息");
            }
            
            boolean success = commentService.deleteComment(commentId, userId);
            if (success) {
                return ApiResponse.success("删除成功");
            } else {
                return ApiResponse.error("删除失败");
            }
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("删除失败: " + e.getMessage());
        }
    }
}

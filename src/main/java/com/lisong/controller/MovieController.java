package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.Movie;
import com.lisong.entity.MovieListData;
import com.lisong.entity.CategoryData;
import com.lisong.service.MovieService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 电影控制器
 */
@RestController
@RequestMapping("/movies")
public class MovieController {
    
    @Autowired
    private MovieService movieService;
    
    /**
     * 获取最新电影列表
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @param genre 类型筛选
     * @param year 年份筛选
     * @param region 地区筛选
     * @param sort 排序方式
     * @return 电影列表响应
     */
    @GetMapping("/latest")
    public ApiResponse<MovieListData> getLatestMovies(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit,
            @RequestParam(value = "genre", required = false) String genre,
            @RequestParam(value = "year", required = false) Integer year,
            @RequestParam(value = "region", required = false) String region,
            @RequestParam(value = "sort", required = false) String sort) {
        try {
            MovieListData data = movieService.getLatestMovies(page, limit, genre, year, region, sort);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取最新电影列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取电影分类
     * @return 分类响应
     */
    @GetMapping("/categories")
    public ApiResponse<CategoryData> getMovieCategories() {
        try {
            CategoryData data = movieService.getMovieCategories();
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取电影分类失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取电影详情
     * @param id 电影ID
     * @return 电影详情响应
     */
    @GetMapping("/{id}")
    public ApiResponse<Movie> getMovieDetail(@PathVariable Long id) {
        try {
            Movie movie = movieService.getMovieDetail(id);
            if (movie == null) {
                return ApiResponse.error(404, "电影不存在");
            }
            return ApiResponse.success(movie);
        } catch (Exception e) {
            return ApiResponse.error("获取电影详情失败: " + e.getMessage());
        }
    }

    // ========== 电影管理接口 ==========

    /**
     * 获取所有电影列表（管理用）
     * @param page 页码，默认1
     * @param limit 每页数量，默认12
     * @return 电影列表响应
     */
    @GetMapping("/admin/all")
    public ApiResponse<MovieListData> getAllMovies(
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "limit", required = false) Integer limit) {
        try {
            MovieListData data = movieService.getAllMovies(page, limit);
            return ApiResponse.success(data);
        } catch (Exception e) {
            return ApiResponse.error("获取电影列表失败: " + e.getMessage());
        }
    }

    /**
     * 创建新电影
     * @param movie 电影信息
     * @return 创建的电影响应
     */
    @PostMapping
    public ApiResponse<Movie> createMovie(@RequestBody Movie movie) {
        try {
            Movie createdMovie = movieService.createMovie(movie);
            return ApiResponse.success(createdMovie);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(500, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("创建电影失败: " + e.getMessage());
        }
    }

    /**
     * 更新电影信息
     * @param id 电影ID
     * @param movie 更新的电影信息
     * @return 更新后的电影响应
     */
    @PutMapping("/{id}")
    public ApiResponse<Movie> updateMovie(@PathVariable Long id, @RequestBody Movie movie) {
        try {
            Movie updatedMovie = movieService.updateMovie(id, movie);
            return ApiResponse.success(updatedMovie);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("更新电影失败: " + e.getMessage());
        }
    }

    /**
     * 删除电影
     * @param id 电影ID
     * @return 删除结果响应
     */
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteMovie(@PathVariable Long id) {
        try {
            boolean deleted = movieService.deleteMovie(id);
            if (deleted) {
                return ApiResponse.success("电影删除成功");
            } else {
                return ApiResponse.error("电影删除失败");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("删除电影失败: " + e.getMessage());
        }
    }
}

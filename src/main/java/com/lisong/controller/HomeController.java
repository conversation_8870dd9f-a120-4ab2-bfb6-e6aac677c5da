package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.HomeData;
import com.lisong.entity.Carousel;
import com.lisong.service.HomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 首页控制器
 */
@RestController
@RequestMapping("/")
public class HomeController {

    @Autowired
    private HomeService homeService;

    /**
     * 获取首页数据
     * @return 首页数据响应
     */
    @GetMapping("/home")
    public ApiResponse<HomeData> getHomeData() {
        try {
            HomeData homeData = homeService.getHomeData();
            return ApiResponse.success(homeData);
        } catch (Exception e) {
            return ApiResponse.error("获取首页数据失败: " + e.getMessage());
        }
    }

    // ========== 轮播图管理接口 ==========

    /**
     * 获取所有轮播图列表
     * @return 轮播图列表响应
     */
    @GetMapping("/carousel")
    public ApiResponse<List<Carousel>> getAllCarousels() {
        try {
            List<Carousel> carousels = homeService.getAllCarousels();
            return ApiResponse.success(carousels);
        } catch (Exception e) {
            return ApiResponse.error("获取轮播图列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取轮播图详情
     * @param id 轮播图ID
     * @return 轮播图详情响应
     */
    @GetMapping("/carousel/{id}")
    public ApiResponse<Carousel> getCarouselById(@PathVariable Long id) {
        try {
            Carousel carousel = homeService.getCarouselById(id);
            if (carousel == null) {
                return ApiResponse.error(404, "轮播图不存在");
            }
            return ApiResponse.success(carousel);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("获取轮播图详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建新轮播图
     * @param carousel 轮播图信息
     * @return 创建的轮播图响应
     */
    @PostMapping("/carousel")
    public ApiResponse<Carousel> createCarousel(@RequestBody Carousel carousel) {
        try {
            Carousel createdCarousel = homeService.createCarousel(carousel);
            return ApiResponse.success(createdCarousel);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(500, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("创建轮播图失败: " + e.getMessage());
        }
    }

    /**
     * 更新轮播图
     * @param id 轮播图ID
     * @param carousel 更新的轮播图信息
     * @return 更新后的轮播图响应
     */
    @PutMapping("/carousel/{id}")
    public ApiResponse<Carousel> updateCarousel(@PathVariable Long id, @RequestBody Carousel carousel) {
        try {
            Carousel updatedCarousel = homeService.updateCarousel(id, carousel);
            return ApiResponse.success(updatedCarousel);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("更新轮播图失败: " + e.getMessage());
        }
    }

    /**
     * 删除轮播图
     * @param id 轮播图ID
     * @return 删除结果响应
     */
    @DeleteMapping("/carousel/{id}")
    public ApiResponse<String> deleteCarousel(@PathVariable Long id) {
        try {
            boolean deleted = homeService.deleteCarousel(id);
            if (deleted) {
                return ApiResponse.success("轮播图删除成功");
            } else {
                return ApiResponse.error("轮播图删除失败");
            }
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("删除轮播图失败: " + e.getMessage());
        }
    }
}

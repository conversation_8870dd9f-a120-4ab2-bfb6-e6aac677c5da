package com.lisong.controller;

import com.lisong.entity.ApiResponse;
import com.lisong.entity.PlayResponse;
import com.lisong.entity.PlayHistory;
import com.lisong.dto.PlayHistoryRequest;
import com.lisong.service.PlayService;
import com.lisong.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import jakarta.servlet.http.HttpServletRequest;

/**
 * 播放控制器
 */
@RestController
@RequestMapping("/play")
public class PlayController {
    
    @Autowired
    private PlayService playService;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    /**
     * 获取播放链接
     * @param type 内容类型：movie, tv, variety
     * @param id 内容ID
     * @param episode 集数（可选）
     * @param quality 清晰度（可选）
     * @return 播放响应
     */
    @GetMapping("/{type}/{id}")
    public ApiResponse<PlayResponse> getPlayUrl(
            @PathVariable String type,
            @PathVariable Long id,
            @RequestParam(required = false) Integer episode,
            @RequestParam(required = false) String quality) {
        try {
            PlayResponse playResponse = playService.getPlayUrl(type, id, episode, quality);
            return ApiResponse.success(playResponse);
        } catch (IllegalArgumentException e) {
            return ApiResponse.error(400, e.getMessage());
        } catch (RuntimeException e) {
            return ApiResponse.error(404, e.getMessage());
        } catch (Exception e) {
            return ApiResponse.error("获取播放链接失败: " + e.getMessage());
        }
    }
    
    /**
     * 记录播放历史
     * @param request 播放历史请求
     * @param httpRequest HTTP请求（用于获取JWT token）
     * @return 播放历史响应
     */
    @PostMapping("/history")
    public ApiResponse<PlayHistory> recordPlayHistory(
            @RequestBody PlayHistoryRequest request,
            HttpServletRequest httpRequest) {
        try {
            // 从请求头获取JWT token
            String authHeader = httpRequest.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ApiResponse.error(401, "未提供有效的认证token");
            }
            
            String token = authHeader.substring(7);

            if (!jwtUtil.validateToken(token)) {
                return ApiResponse.error(401, "无效的认证token");
            }
            
            // 从token中获取用户ID（这里简化处理，实际项目中应该从数据库查询）
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return ApiResponse.error(401, "无法获取用户信息");
            }
            
            PlayHistory playHistory = playService.recordPlayHistory(userId, request);
            return ApiResponse.success(playHistory);
        } catch (Exception e) {
            return ApiResponse.error("记录播放历史失败: " + e.getMessage());
        }
    }
}

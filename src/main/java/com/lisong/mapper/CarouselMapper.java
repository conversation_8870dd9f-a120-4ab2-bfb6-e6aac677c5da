package com.lisong.mapper;

import com.lisong.entity.Carousel;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 轮播图数据访问接口
 */
@Mapper
public interface CarouselMapper {

    /**
     * 获取启用的轮播图列表，按排序字段排序
     */
    @Select("SELECT id, title, description, image, url, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM carousel WHERE status = 1 ORDER BY sort_order ASC, id ASC")
    List<Carousel> findActiveCarousels();

    /**
     * 根据ID查询轮播图
     */
    @Select("SELECT id, title, description, image, url, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM carousel WHERE id = #{id}")
    Carousel findById(Long id);

    /**
     * 获取所有轮播图
     */
    @Select("SELECT id, title, description, image, url, sort_order as sortOrder, status, created_at as createdAt, updated_at as updatedAt FROM carousel ORDER BY sort_order ASC, id ASC")
    List<Carousel> findAll();

    /**
     * 插入新轮播图
     */
    @Insert("INSERT INTO carousel (title, description, image, url, sort_order, status, created_at, updated_at) " +
            "VALUES (#{title}, #{description}, #{image}, #{url}, #{sortOrder}, #{status}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Carousel carousel);

    /**
     * 更新轮播图
     */
    @Update("UPDATE carousel SET title = #{title}, description = #{description}, image = #{image}, " +
            "url = #{url}, sort_order = #{sortOrder}, status = #{status}, updated_at = CURRENT_TIMESTAMP " +
            "WHERE id = #{id}")
    int update(Carousel carousel);

    /**
     * 删除轮播图（软删除）
     */
    @Update("UPDATE carousel SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 获取轮播图总数
     */
    @Select("SELECT COUNT(*) FROM carousel WHERE status = 1")
    Long countActive();

    /**
     * 获取所有轮播图总数（包括已删除）
     */
    @Select("SELECT COUNT(*) FROM carousel")
    Long countAll();
}

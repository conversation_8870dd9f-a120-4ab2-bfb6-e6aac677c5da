package com.lisong.mapper;

import com.lisong.entity.TvSeries;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 电视剧数据访问接口
 */
@Mapper
public interface TvSeriesMapper {
    
    /**
     * 根据条件查询电视剧列表（分页）
     */
    @Select("<script>" +
            "SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<if test='seriesStatus != null and seriesStatus != \"\"'>" +
            "AND series_status = #{seriesStatus} " +
            "</if>" +
            "<choose>" +
            "<when test='sort == \"rating\"'>" +
            "ORDER BY rating DESC, view_count DESC " +
            "</when>" +
            "<when test='sort == \"popular\"'>" +
            "ORDER BY view_count DESC, rating DESC " +
            "</when>" +
            "<when test='sort == \"name\"'>" +
            "ORDER BY title ASC " +
            "</when>" +
            "<otherwise>" +
            "ORDER BY created_at DESC, rating DESC " +
            "</otherwise>" +
            "</choose>" +
            "LIMIT #{limit} OFFSET #{offset}" +
            "</script>")
    List<TvSeries> findTvSeriesWithFilter(@Param("genre") String genre, 
                                         @Param("region") String region, 
                                         @Param("seriesStatus") String seriesStatus, 
                                         @Param("sort") String sort, 
                                         @Param("offset") Integer offset, 
                                         @Param("limit") Integer limit);
    
    /**
     * 根据条件统计电视剧数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM tv_series WHERE status = 1 " +
            "<if test='genre != null and genre != \"\"'>" +
            "AND genre LIKE CONCAT('%', #{genre}, '%') " +
            "</if>" +
            "<if test='region != null and region != \"\"'>" +
            "AND region = #{region} " +
            "</if>" +
            "<if test='seriesStatus != null and seriesStatus != \"\"'>" +
            "AND series_status = #{seriesStatus} " +
            "</if>" +
            "</script>")
    Long countTvSeriesWithFilter(@Param("genre") String genre, 
                                @Param("region") String region, 
                                @Param("seriesStatus") String seriesStatus);
    
    /**
     * 根据ID查询电视剧详情
     */
    @Select("SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series WHERE id = #{id} AND status = 1")
    TvSeries findById(Long id);
    
    /**
     * 获取电视剧总数（用于统计）
     */
    @Select("SELECT COUNT(*) FROM tv_series WHERE status = 1")
    Long countTvSeries();

    /**
     * 插入新电视剧
     */
    @Insert("INSERT INTO tv_series (title, poster, backdrop, rating, series_year, genre, region, director, \"cast\", description, series_status, current_episode, total_episodes, update_day, update_time, first_air_date, view_count, like_count, status, created_at, updated_at) " +
            "VALUES (#{title}, #{poster}, #{backdrop}, #{rating}, #{seriesYear}, #{genre}, #{region}, #{director}, #{cast}, #{description}, #{seriesStatus}, #{currentEpisode}, #{totalEpisodes}, #{updateDay}, #{updateTime}, #{firstAirDate}, #{viewCount}, #{likeCount}, #{status}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(TvSeries tvSeries);

    /**
     * 更新电视剧信息
     */
    @Update("UPDATE tv_series SET title = #{title}, poster = #{poster}, backdrop = #{backdrop}, rating = #{rating}, " +
            "series_year = #{seriesYear}, genre = #{genre}, region = #{region}, director = #{director}, \"cast\" = #{cast}, " +
            "description = #{description}, series_status = #{seriesStatus}, current_episode = #{currentEpisode}, " +
            "total_episodes = #{totalEpisodes}, update_day = #{updateDay}, update_time = #{updateTime}, " +
            "first_air_date = #{firstAirDate}, view_count = #{viewCount}, like_count = #{likeCount}, " +
            "status = #{status}, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int update(TvSeries tvSeries);

    /**
     * 删除电视剧（软删除）
     */
    @Update("UPDATE tv_series SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int deleteById(Long id);

    /**
     * 获取所有电视剧（包括已删除的，用于管理）
     */
    @Select("SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series ORDER BY created_at DESC")
    List<TvSeries> findAllForAdmin();

    /**
     * 分页获取所有电视剧（管理用）
     */
    @Select("SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series ORDER BY created_at DESC LIMIT #{offset}, #{limit}")
    List<TvSeries> findAllWithPagination(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 获取电视剧总数（包括已删除的）
     */
    @Select("SELECT COUNT(*) FROM tv_series")
    Long countAllTvSeries();

    /**
     * 根据ID查询电视剧（包括已删除的，用于管理）
     */
    @Select("SELECT id, title, poster, backdrop, rating, series_year as seriesYear, genre, region, director, \"cast\", " +
            "description, series_status as seriesStatus, current_episode as currentEpisode, total_episodes as totalEpisodes, " +
            "update_day as updateDay, update_time as updateTime, first_air_date as firstAirDate, " +
            "view_count as viewCount, like_count as likeCount, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM tv_series WHERE id = #{id}")
    TvSeries findByIdForAdmin(Long id);
}

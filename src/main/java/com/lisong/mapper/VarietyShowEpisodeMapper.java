package com.lisong.mapper;

import com.lisong.entity.VarietyShowEpisode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 综艺节目期数数据访问接口
 */
@Mapper
public interface VarietyShowEpisodeMapper {
    
    /**
     * 根据综艺节目ID查询期数列表
     */
    @Select("SELECT id, show_id as showId, episode_number as episodeNumber, title, air_date as airDate, " +
            "duration, guests, play_url as playUrl, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM variety_show_episode WHERE show_id = #{showId} AND status = 1 " +
            "ORDER BY episode_number ASC")
    List<VarietyShowEpisode> findByShowId(@Param("showId") Long showId);
    
    /**
     * 根据ID查询期数
     */
    @Select("SELECT id, show_id as showId, episode_number as episodeNumber, title, air_date as airDate, " +
            "duration, guests, play_url as playUrl, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM variety_show_episode WHERE id = #{id} AND status = 1")
    VarietyShowEpisode findById(Long id);

    /**
     * 根据综艺节目ID和期数查询期数
     */
    @Select("SELECT id, show_id as showId, episode_number as episodeNumber, title, air_date as airDate, " +
            "duration, guests, play_url as playUrl, status, created_at as createdAt, updated_at as updatedAt " +
            "FROM variety_show_episode WHERE show_id = #{showId} AND episode_number = #{episodeNumber} AND status = 1")
    VarietyShowEpisode findByShowIdAndEpisode(@Param("showId") Long showId, @Param("episodeNumber") Integer episodeNumber);
}

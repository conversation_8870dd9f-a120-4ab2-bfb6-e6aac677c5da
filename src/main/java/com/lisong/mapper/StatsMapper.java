package com.lisong.mapper;

import com.lisong.entity.TopGenre;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import java.math.BigDecimal;
import java.util.List;

/**
 * 统计数据访问接口
 */
@Mapper
public interface StatsMapper {

    /**
     * 获取电影总数
     */
    @Select("SELECT COUNT(*) FROM movie WHERE status = 1")
    Long countMovies();

    /**
     * 获取电视剧总数
     */
    @Select("SELECT COUNT(*) FROM tv_series WHERE status = 1")
    Long countTVShows();

    /**
     * 获取综艺节目总数
     */
    @Select("SELECT COUNT(*) FROM variety_show WHERE status = 1")
    Long countVarietyShows();

    /**
     * 获取用户总数
     */
    @Select("SELECT COUNT(*) FROM \"user\" WHERE status = 1")
    Long countUsers();

    /**
     * 获取总观看数
     */
    @Select("SELECT COALESCE(SUM(view_count), 0) FROM (" +
            "SELECT view_count FROM movie WHERE status = 1 " +
            "UNION ALL " +
            "SELECT view_count FROM tv_series WHERE status = 1 " +
            "UNION ALL " +
            "SELECT view_count FROM variety_show WHERE status = 1" +
            ") AS all_views")
    Long getTotalViews();

    /**
     * 获取本月新增内容数量
     */
    @Select("SELECT COUNT(*) FROM (" +
            "SELECT created_at FROM movie WHERE status = 1 AND YEAR(created_at) = YEAR(CURRENT_DATE) AND MONTH(created_at) = MONTH(CURRENT_DATE) " +
            "UNION ALL " +
            "SELECT created_at FROM tv_series WHERE status = 1 AND YEAR(created_at) = YEAR(CURRENT_DATE) AND MONTH(created_at) = MONTH(CURRENT_DATE) " +
            "UNION ALL " +
            "SELECT created_at FROM variety_show WHERE status = 1 AND YEAR(created_at) = YEAR(CURRENT_DATE) AND MONTH(created_at) = MONTH(CURRENT_DATE)" +
            ") AS monthly_content")
    Long getMonthlyNewContent();

    /**
     * 获取平均评分
     */
    @Select("SELECT AVG(rating) FROM (" +
            "SELECT rating FROM movie WHERE status = 1 AND rating IS NOT NULL " +
            "UNION ALL " +
            "SELECT rating FROM tv_series WHERE status = 1 AND rating IS NOT NULL " +
            "UNION ALL " +
            "SELECT rating FROM variety_show WHERE status = 1 AND rating IS NOT NULL" +
            ") AS all_ratings")
    BigDecimal getAverageRating();

    /**
     * 获取热门类型统计（前3名）
     * 这里简化处理，统计包含特定关键词的类型
     */
    @Select("SELECT genre_name as name, COUNT(*) as count FROM (" +
            "SELECT '动作' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%动作%' " +
            "UNION ALL SELECT '动作' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%动作%' " +
            "UNION ALL SELECT '动作' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%动作%' " +
            "UNION ALL " +
            "SELECT '剧情' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%剧情%' " +
            "UNION ALL SELECT '剧情' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%剧情%' " +
            "UNION ALL SELECT '剧情' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%剧情%' " +
            "UNION ALL " +
            "SELECT '喜剧' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%喜剧%' " +
            "UNION ALL SELECT '喜剧' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%喜剧%' " +
            "UNION ALL SELECT '喜剧' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%喜剧%' " +
            "UNION ALL " +
            "SELECT '科幻' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%科幻%' " +
            "UNION ALL SELECT '科幻' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%科幻%' " +
            "UNION ALL SELECT '科幻' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%科幻%' " +
            "UNION ALL " +
            "SELECT '爱情' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%爱情%' " +
            "UNION ALL SELECT '爱情' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%爱情%' " +
            "UNION ALL SELECT '爱情' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%爱情%' " +
            "UNION ALL " +
            "SELECT '悬疑' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%悬疑%' " +
            "UNION ALL SELECT '悬疑' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%悬疑%' " +
            "UNION ALL SELECT '悬疑' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%悬疑%' " +
            "UNION ALL " +
            "SELECT '犯罪' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%犯罪%' " +
            "UNION ALL SELECT '犯罪' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%犯罪%' " +
            "UNION ALL SELECT '犯罪' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%犯罪%' " +
            "UNION ALL " +
            "SELECT '冒险' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%冒险%' " +
            "UNION ALL SELECT '冒险' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%冒险%' " +
            "UNION ALL SELECT '冒险' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%冒险%' " +
            "UNION ALL " +
            "SELECT '奇幻' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%奇幻%' " +
            "UNION ALL SELECT '奇幻' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%奇幻%' " +
            "UNION ALL SELECT '奇幻' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%奇幻%' " +
            "UNION ALL " +
            "SELECT '动画' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%动画%' " +
            "UNION ALL SELECT '动画' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%动画%' " +
            "UNION ALL SELECT '动画' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%动画%' " +
            "UNION ALL " +
            "SELECT '真人秀' as genre_name FROM movie WHERE status = 1 AND genre LIKE '%真人秀%' " +
            "UNION ALL SELECT '真人秀' as genre_name FROM tv_series WHERE status = 1 AND genre LIKE '%真人秀%' " +
            "UNION ALL SELECT '真人秀' as genre_name FROM variety_show WHERE status = 1 AND genre LIKE '%真人秀%' " +
            ") AS all_genres " +
            "GROUP BY genre_name " +
            "ORDER BY count DESC " +
            "LIMIT 3")
    List<TopGenre> getTopGenres();
}

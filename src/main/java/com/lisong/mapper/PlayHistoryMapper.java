package com.lisong.mapper;

import com.lisong.entity.PlayHistory;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 播放历史数据访问层
 */
@Mapper
public interface PlayHistoryMapper {
    
    /**
     * 插入播放历史记录
     */
    @Insert("INSERT INTO play_history (user_id, content_id, content_type, episode, progress, duration, quality, watched_at) " +
            "VALUES (#{userId}, #{contentId}, #{contentType}, #{episode}, #{progress}, #{duration}, #{quality}, CURRENT_TIMESTAMP)")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(PlayHistory playHistory);
    
    /**
     * 更新播放历史记录
     */
    @Update("UPDATE play_history SET progress = #{progress}, duration = #{duration}, quality = #{quality}, " +
            "watched_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP " +
            "WHERE user_id = #{userId} AND content_id = #{contentId} AND content_type = #{contentType} AND episode = #{episode}")
    int update(PlayHistory playHistory);
    
    /**
     * 查找用户的播放历史记录
     */
    @Select("SELECT * FROM play_history WHERE user_id = #{userId} AND content_id = #{contentId} " +
            "AND content_type = #{contentType} AND episode = #{episode}")
    PlayHistory findByUserAndContent(@Param("userId") Long userId, @Param("contentId") Long contentId, 
                                   @Param("contentType") String contentType, @Param("episode") Integer episode);
    
    /**
     * 获取用户播放历史列表
     */
    @Select("SELECT * FROM play_history WHERE user_id = #{userId} ORDER BY watched_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<PlayHistory> findByUserId(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 删除播放历史记录
     */
    @Delete("DELETE FROM play_history WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
}

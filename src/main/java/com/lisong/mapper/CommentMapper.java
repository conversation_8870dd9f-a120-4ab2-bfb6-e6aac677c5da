package com.lisong.mapper;

import com.lisong.entity.Comment;
import org.apache.ibatis.annotations.*;
import java.util.List;

/**
 * 评论数据访问层
 */
@Mapper
public interface CommentMapper {
    
    /**
     * 插入评论
     */
    @Insert("INSERT INTO comment (user_id, content_id, content_type, parent_id, content, rating, likes, status) " +
            "VALUES (#{userId}, #{contentId}, #{contentType}, #{parentId}, #{content}, #{rating}, #{likes}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Comment comment);
    
    /**
     * 根据内容获取主评论列表（不包括回复）
     */
    @Select("SELECT c.id, c.user_id as userId, c.content_id as contentId, c.content_type as contentType, " +
            "c.parent_id as parentId, c.content, c.rating, c.likes, c.status, " +
            "c.created_at as createdAt, c.updated_at as updatedAt, " +
            "u.username, u.avatar " +
            "FROM comment c " +
            "LEFT JOIN \"user\" u ON c.user_id = u.id " +
            "WHERE c.content_id = #{contentId} AND c.content_type = #{contentType} " +
            "AND c.parent_id IS NULL AND c.status = 1 " +
            "ORDER BY ${orderBy} " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<Comment> findMainCommentsByContent(@Param("contentId") Long contentId, 
                                          @Param("contentType") String contentType,
                                          @Param("orderBy") String orderBy,
                                          @Param("offset") int offset, 
                                          @Param("limit") int limit);
    
    /**
     * 根据父评论ID获取回复列表
     */
    @Select("SELECT c.id, c.user_id as userId, c.content_id as contentId, c.content_type as contentType, " +
            "c.parent_id as parentId, c.content, c.rating, c.likes, c.status, " +
            "c.created_at as createdAt, c.updated_at as updatedAt, " +
            "u.username, u.avatar " +
            "FROM comment c " +
            "LEFT JOIN \"user\" u ON c.user_id = u.id " +
            "WHERE c.parent_id = #{parentId} AND c.status = 1 " +
            "ORDER BY c.created_at ASC")
    List<Comment> findRepliesByParentId(@Param("parentId") Long parentId);
    
    /**
     * 获取内容的主评论总数
     */
    @Select("SELECT COUNT(*) FROM comment " +
            "WHERE content_id = #{contentId} AND content_type = #{contentType} " +
            "AND parent_id IS NULL AND status = 1")
    int countMainCommentsByContent(@Param("contentId") Long contentId, @Param("contentType") String contentType);
    
    /**
     * 根据ID查询评论
     */
    @Select("SELECT c.id, c.user_id as userId, c.content_id as contentId, c.content_type as contentType, " +
            "c.parent_id as parentId, c.content, c.rating, c.likes, c.status, " +
            "c.created_at as createdAt, c.updated_at as updatedAt, " +
            "u.username, u.avatar " +
            "FROM comment c " +
            "LEFT JOIN \"user\" u ON c.user_id = u.id " +
            "WHERE c.id = #{id} AND c.status = 1")
    Comment findById(@Param("id") Long id);
    
    /**
     * 更新评论点赞数
     */
    @Update("UPDATE comment SET likes = #{likes}, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int updateLikes(@Param("id") Long id, @Param("likes") Integer likes);
    
    /**
     * 删除评论（软删除）
     */
    @Update("UPDATE comment SET status = 0, updated_at = CURRENT_TIMESTAMP WHERE id = #{id}")
    int deleteById(@Param("id") Long id);
    
    /**
     * 获取用户的评论列表
     */
    @Select("SELECT c.id, c.user_id as userId, c.content_id as contentId, c.content_type as contentType, " +
            "c.parent_id as parentId, c.content, c.rating, c.likes, c.status, " +
            "c.created_at as createdAt, c.updated_at as updatedAt, " +
            "u.username, u.avatar " +
            "FROM comment c " +
            "LEFT JOIN \"user\" u ON c.user_id = u.id " +
            "WHERE c.user_id = #{userId} AND c.status = 1 " +
            "ORDER BY c.created_at DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<Comment> findByUserId(@Param("userId") Long userId, @Param("offset") int offset, @Param("limit") int limit);
}

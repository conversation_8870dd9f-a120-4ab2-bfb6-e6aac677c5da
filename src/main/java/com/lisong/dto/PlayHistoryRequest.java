package com.lisong.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 播放历史请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlayHistoryRequest {
    
    private Long contentId;
    private String contentType; // movie, tv, variety
    private Integer episode; // 集数，电影默认为1
    private Integer progress; // 播放进度（秒）
    private Integer duration; // 总时长（秒）
}

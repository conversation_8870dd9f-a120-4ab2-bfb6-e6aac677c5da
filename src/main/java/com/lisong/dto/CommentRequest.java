package com.lisong.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 评论请求DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommentRequest {
    
    private Long contentId;
    private String contentType; // movie, tv, variety
    private String content; // 评论内容
    private Integer rating; // 评分（1-5），可选
    private Long parentId; // 父评论ID，回复时使用
}

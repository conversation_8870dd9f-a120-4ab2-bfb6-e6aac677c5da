# 用户认证系统 API 测试示例

## 基础信息
- 服务器地址: http://localhost:8080
- 数据库控制台: http://localhost:8080/h2-console
  - JDBC URL: jdbc:h2:mem:testdb
  - 用户名: sa
  - 密码: password

## 1. 认证接口

### 1.1 用户注册
```bash
curl -X POST http://localhost:8080/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirmPassword": "password123"
  }'
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 7,
    "username": "testuser",
    "email": "<EMAIL>",
    "avatar": null,
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 86400
  }
}
```

### 1.2 用户登录
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

### 1.3 验证令牌
```bash
curl -X GET http://localhost:8080/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 1.4 刷新令牌
```bash
curl -X POST http://localhost:8080/auth/refresh \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 1.5 用户登出
```bash
curl -X POST http://localhost:8080/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 2. 用户接口

### 2.1 获取用户信息
```bash
curl -X GET http://localhost:8080/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 7,
    "username": "testuser",
    "email": "<EMAIL>",
    "avatar": null,
    "joinDate": "2025-06-16",
    "watchHistory": 0,
    "favorites": 0,
    "vipLevel": "regular"
  }
}
```

### 2.2 更新用户信息
```bash
curl -X PUT http://localhost:8080/user/profile \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "新昵称",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "gender": "male",
    "avatar": "https://example.com/avatar.jpg"
  }'
```

### 2.3 添加收藏
```bash
curl -X POST http://localhost:8080/user/favorites \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1,
    "contentType": "movie",
    "action": "add"
  }'
```

### 2.4 取消收藏
```bash
curl -X POST http://localhost:8080/user/favorites \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1,
    "contentType": "movie",
    "action": "remove"
  }'
```

### 2.5 获取收藏列表
```bash
curl -X GET "http://localhost:8080/user/favorites?contentType=movie&page=0&size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2.6 检查是否已收藏
```bash
curl -X GET "http://localhost:8080/user/favorites/check?contentId=1&contentType=movie" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 2.7 修改密码
```bash
curl -X PUT http://localhost:8080/user/password \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "oldPassword": "password123",
    "newPassword": "newpassword123"
  }'
```

## 3. 管理员接口 (需要ADMIN角色)

### 3.1 获取所有用户
```bash
curl -X GET "http://localhost:8080/admin/users?page=0&size=10" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.2 搜索用户
```bash
curl -X GET "http://localhost:8080/admin/users?keyword=test&page=0&size=10" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.3 获取用户详情
```bash
curl -X GET http://localhost:8080/admin/users/7 \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.4 创建用户
```bash
curl -X POST http://localhost:8080/admin/users \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "password": "password123",
    "email": "<EMAIL>",
    "nickname": "新用户",
    "vipLevel": "premium"
  }'
```

### 3.5 更新用户
```bash
curl -X PUT http://localhost:8080/admin/users/7 \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "nickname": "更新的昵称",
    "vipLevel": "vip"
  }'
```

### 3.6 删除用户
```bash
curl -X DELETE http://localhost:8080/admin/users/7 \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.7 为用户分配角色
```bash
curl -X POST http://localhost:8080/admin/users/7/roles/1 \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.8 移除用户角色
```bash
curl -X DELETE http://localhost:8080/admin/users/7/roles/1 \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.9 获取所有角色
```bash
curl -X GET http://localhost:8080/admin/roles \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 3.10 创建角色
```bash
curl -X POST http://localhost:8080/admin/roles \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "MODERATOR",
    "description": "版主角色"
  }'
```

## 4. 示例数据

### 预设用户账号
- 管理员: admin / password123 (需要修复密码加密)
- 普通用户: user001-user005 / password123

### 预设角色
- ADMIN: 系统管理员
- USER: 普通用户

### 测试内容
- 电影: ID 1-13
- 电视剧: ID 1-10  
- 综艺节目: ID 1-12

## 5. 错误处理

所有接口都返回统一的响应格式:
```json
{
  "code": 200,
  "message": "success",
  "data": {...}
}
```

错误响应示例:
```json
{
  "code": 401,
  "message": "未授权访问，请先登录",
  "data": null
}
```

## 6. 注意事项

1. 所有需要认证的接口都需要在请求头中包含 `Authorization: Bearer TOKEN`
2. 管理员接口需要用户拥有 ADMIN 角色
3. JWT令牌默认有效期为24小时
4. 密码使用BCrypt加密
5. 数据库使用H2内存数据库，重启后数据会丢失

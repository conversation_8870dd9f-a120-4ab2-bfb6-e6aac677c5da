# 首页和电影 API 接口文档

## 基础信息
- 服务器地址: http://localhost:8080
- 数据库控制台: http://localhost:8080/h2-console
  - JDBC URL: jdbc:h2:mem:testdb
  - 用户名: sa
  - 密码: password

## 1. 首页接口

### 1.1 获取首页数据
**接口地址**: `GET /home`

**描述**: 获取首页展示的所有数据，包括轮播图、热门电影、最新电影和统计信息

**请求示例**:
```bash
curl -X GET http://localhost:8080/home
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "carousel": [
      {
        "id": 1,
        "title": "阿凡达：水之道",
        "description": "詹姆斯·卡梅隆执导的科幻史诗巨作",
        "image": "https://example.com/avatar2.jpg",
        "url": "/movie/1",
        "sortOrder": 1,
        "status": 1,
        "createdAt": "2025-06-16T10:00:00",
        "updatedAt": "2025-06-16T10:00:00"
      }
    ],
    "hotMovies": [
      {
        "id": 1,
        "title": "阿凡达：水之道",
        "originalTitle": "Avatar: The Way of Water",
        "poster": "https://example.com/avatar2-poster.jpg",
        "rating": 8.5,
        "movieYear": 2022,
        "duration": 192,
        "genre": "[\"科幻\", \"动作\", \"冒险\"]",
        "region": "美国",
        "director": "詹姆斯·卡梅隆",
        "viewCount": 1500000,
        "likeCount": 85000
      }
    ],
    "latestMovies": [...],
    "stats": {
      "totalMovies": 11,
      "totalTVShows": 5,
      "totalVarietyShows": 5,
      "totalUsers": 5
    }
  }
}
```

## 2. 轮播图管理接口

### 2.1 获取所有轮播图
**接口地址**: `GET /carousel`

**描述**: 获取所有轮播图列表（包括已禁用的）

**请求示例**:
```bash
curl -X GET http://localhost:8080/carousel
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "阿凡达：水之道",
      "description": "詹姆斯·卡梅隆执导的科幻史诗巨作",
      "image": "https://example.com/avatar2.jpg",
      "url": "/movie/1",
      "sortOrder": 1,
      "status": 1,
      "createdAt": "2025-06-16T10:00:00",
      "updatedAt": "2025-06-16T10:00:00"
    }
  ]
}
```

### 2.2 获取轮播图详情
**接口地址**: `GET /carousel/{id}`

**描述**: 根据ID获取指定轮播图的详细信息

**请求参数**:
- `id` (path): 轮播图ID

**请求示例**:
```bash
curl -X GET http://localhost:8080/carousel/1
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "阿凡达：水之道",
    "description": "詹姆斯·卡梅隆执导的科幻史诗巨作",
    "image": "https://example.com/avatar2.jpg",
    "url": "/movie/1",
    "sortOrder": 1,
    "status": 1,
    "createdAt": "2025-06-16T10:00:00",
    "updatedAt": "2025-06-16T10:00:00"
  }
}
```

### 2.3 创建轮播图
**接口地址**: `POST /carousel`

**描述**: 创建新的轮播图

**请求体参数**:
- `title` (string, 必填): 轮播图标题
- `description` (string, 可选): 轮播图描述
- `image` (string, 必填): 轮播图图片URL
- `url` (string, 可选): 点击跳转链接
- `sortOrder` (int, 可选): 排序顺序，默认0
- `status` (int, 可选): 状态，1启用/0禁用，默认1

**请求示例**:
```bash
curl -X POST http://localhost:8080/carousel \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新电影预告",
    "description": "精彩电影即将上映",
    "image": "https://example.com/movie-poster.jpg",
    "url": "/movie/123",
    "sortOrder": 1,
    "status": 1
  }'
```

### 2.4 更新轮播图
**接口地址**: `PUT /carousel/{id}`

**描述**: 更新指定ID的轮播图信息

**请求参数**:
- `id` (path): 轮播图ID

**请求体参数**: 同创建接口

**请求示例**:
```bash
curl -X PUT http://localhost:8080/carousel/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "description": "更新后的描述",
    "image": "https://example.com/new-poster.jpg",
    "url": "/movie/456",
    "sortOrder": 2,
    "status": 1
  }'
```

### 2.5 删除轮播图
**接口地址**: `DELETE /carousel/{id}`

**描述**: 删除指定ID的轮播图（软删除）

**请求参数**:
- `id` (path): 轮播图ID

**请求示例**:
```bash
curl -X DELETE http://localhost:8080/carousel/1
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "轮播图删除成功"
}
```

## 3. 电影接口

### 3.1 获取最新电影列表
**接口地址**: `GET /movies/latest`

**描述**: 获取最新电影列表，支持分页、筛选和排序

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认12
- `genre` (string, 可选): 类型筛选，如"科幻"
- `year` (int, 可选): 年份筛选，如2023
- `region` (string, 可选): 地区筛选，如"美国"、"中国"
- `sort` (string, 可选): 排序方式
  - `latest`: 最新发布（默认）
  - `rating`: 按评分排序
  - `popular`: 按热度排序
  - `name`: 按名称排序

**请求示例**:
```bash
curl -X GET "http://localhost:8080/movies/latest?page=1&limit=10&genre=科幻&year=2023&sort=rating"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "movies": [
      {
        "id": 1,
        "title": "阿凡达：水之道",
        "originalTitle": "Avatar: The Way of Water",
        "poster": "https://example.com/avatar2-poster.jpg",
        "backdrop": "https://example.com/avatar2-backdrop.jpg",
        "rating": 8.5,
        "movieYear": 2022,
        "duration": 192,
        "genre": "[\"科幻\", \"动作\", \"冒险\"]",
        "region": "美国",
        "director": "詹姆斯·卡梅隆",
        "cast": "[\"萨姆·沃辛顿\", \"佐伊·索尔达娜\"]",
        "description": "杰克·萨利一家在潘多拉星球的新冒险",
        "releaseDate": "2022-12-16",
        "boxOffice": "23.2亿美元",
        "trailerUrl": "https://example.com/avatar2-trailer.mp4",
        "playUrl": "https://example.com/avatar2-play.mp4",
        "movieStatus": "released",
        "isHot": 1,
        "isLatest": 1,
        "viewCount": 1500000,
        "likeCount": 85000,
        "status": 1,
        "createdAt": "2025-06-16T10:00:00",
        "updatedAt": "2025-06-16T10:00:00"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

### 3.2 获取电影分类
**接口地址**: `GET /movies/categories`

**描述**: 获取电影分类列表和统计信息

**请求示例**:
```bash
curl -X GET http://localhost:8080/movies/categories
```

### 3.3 获取电影详情
**接口地址**: `GET /movies/{id}`

**描述**: 根据ID获取电影详细信息

**请求参数**:
- `id` (path): 电影ID

**请求示例**:
```bash
curl -X GET http://localhost:8080/movies/1
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "阿凡达：水之道",
    "originalTitle": "Avatar: The Way of Water",
    "poster": "https://example.com/avatar2-poster.jpg",
    "backdrop": "https://example.com/avatar2-backdrop.jpg",
    "rating": 8.5,
    "movieYear": 2022,
    "duration": 192,
    "genre": "[\"科幻\", \"动作\", \"冒险\"]",
    "region": "美国",
    "director": "詹姆斯·卡梅隆",
    "cast": "[\"萨姆·沃辛顿\", \"佐伊·索尔达娜\"]",
    "description": "杰克·萨利一家在潘多拉星球的新冒险",
    "releaseDate": "2022-12-16",
    "boxOffice": "23.2亿美元",
    "trailerUrl": "https://example.com/avatar2-trailer.mp4",
    "playUrl": "https://example.com/avatar2-play.mp4",
    "movieStatus": "released",
    "isHot": 1,
    "isLatest": 1,
    "viewCount": 1500000,
    "likeCount": 85000,
    "status": 1,
    "createdAt": "2025-06-16T10:00:00",
    "updatedAt": "2025-06-16T10:00:00"
  }
}
```

## 4. 电影管理接口

### 4.1 获取所有电影（管理用）
**接口地址**: `GET /movies/admin/all`

**描述**: 获取所有电影列表（包括已删除的），用于管理后台

**请求参数**:
- `page` (int, 可选): 页码，默认1
- `limit` (int, 可选): 每页数量，默认12

**请求示例**:
```bash
curl -X GET "http://localhost:8080/movies/admin/all?page=1&limit=20"
```

**响应示例**: 同获取最新电影列表

### 4.2 创建电影
**接口地址**: `POST /movies`

**描述**: 创建新的电影记录

**请求体参数**:
- `title` (string, 必填): 电影标题
- `originalTitle` (string, 可选): 原始标题
- `poster` (string, 可选): 海报图片URL
- `backdrop` (string, 可选): 背景图片URL
- `rating` (decimal, 可选): 评分，默认0.0
- `movieYear` (int, 可选): 上映年份
- `duration` (int, 可选): 时长（分钟）
- `genre` (string, 可选): 类型（JSON格式）
- `region` (string, 可选): 地区
- `director` (string, 可选): 导演
- `cast` (string, 可选): 演员（JSON格式）
- `description` (string, 可选): 电影描述
- `releaseDate` (date, 可选): 上映日期
- `boxOffice` (string, 可选): 票房
- `trailerUrl` (string, 可选): 预告片URL
- `playUrl` (string, 可选): 播放URL
- `movieStatus` (string, 可选): 电影状态，默认"released"
- `isHot` (int, 可选): 是否热门，默认0
- `isLatest` (int, 可选): 是否最新，默认0

**请求示例**:
```bash
curl -X POST http://localhost:8080/movies \
  -H "Content-Type: application/json" \
  -d '{
    "title": "阿凡达3",
    "originalTitle": "Avatar 3",
    "poster": "https://example.com/avatar3-poster.jpg",
    "backdrop": "https://example.com/avatar3-backdrop.jpg",
    "rating": 8.5,
    "movieYear": 2024,
    "duration": 180,
    "genre": "[\"科幻\", \"动作\", \"冒险\"]",
    "region": "美国",
    "director": "詹姆斯·卡梅隆",
    "cast": "[\"萨姆·沃辛顿\", \"佐伊·索尔达娜\"]",
    "description": "阿凡达系列第三部",
    "releaseDate": "2024-12-20",
    "boxOffice": "待定",
    "trailerUrl": "https://example.com/avatar3-trailer.mp4",
    "playUrl": "https://example.com/avatar3-play.mp4",
    "movieStatus": "upcoming"
  }'
```

### 4.3 更新电影
**接口地址**: `PUT /movies/{id}`

**描述**: 更新指定ID的电影信息

**请求参数**:
- `id` (path): 电影ID

**请求体参数**: 同创建接口

**请求示例**:
```bash
curl -X PUT http://localhost:8080/movies/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "阿凡达3：火之纪元",
    "rating": 9.0,
    "boxOffice": "20亿美元",
    "movieStatus": "released"
  }'
```

### 4.4 删除电影
**接口地址**: `DELETE /movies/{id}`

**描述**: 删除指定ID的电影（软删除）

**请求参数**:
- `id` (path): 电影ID

**请求示例**:
```bash
curl -X DELETE http://localhost:8080/movies/1
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "电影删除成功"
}
```

## 5. 错误处理

所有接口都返回统一的响应格式:
```json
{
  "code": 200,
  "message": "success",
  "data": {...}
}
```

错误响应示例:
```json
{
  "code": 400,
  "message": "电影标题不能为空",
  "data": null
}
```

常见错误码:
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 6. 数据字段说明

### 轮播图字段
- `id`: 轮播图ID
- `title`: 标题
- `description`: 描述
- `image`: 图片URL
- `url`: 跳转链接
- `sortOrder`: 排序顺序
- `status`: 状态（1启用/0禁用）
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

### 电影字段
- `id`: 电影ID
- `title`: 电影标题
- `originalTitle`: 原始标题
- `poster`: 海报图片URL
- `backdrop`: 背景图片URL
- `rating`: 评分（0.0-10.0）
- `movieYear`: 上映年份
- `duration`: 时长（分钟）
- `genre`: 类型（JSON格式数组）
- `region`: 地区
- `director`: 导演
- `cast`: 演员（JSON格式数组）
- `description`: 电影描述
- `releaseDate`: 上映日期
- `boxOffice`: 票房
- `trailerUrl`: 预告片URL
- `playUrl`: 播放URL
- `movieStatus`: 电影状态（released/upcoming/cancelled）
- `isHot`: 是否热门（1是/0否）
- `isLatest`: 是否最新（1是/0否）
- `viewCount`: 观看次数
- `likeCount`: 点赞次数
- `status`: 状态（1正常/0删除）
- `createdAt`: 创建时间
- `updatedAt`: 更新时间

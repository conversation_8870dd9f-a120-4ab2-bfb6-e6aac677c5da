# 统计接口 API 文档

## 接口概述

统计接口提供网站整体数据统计功能，包括内容数量、用户数量、观看数据、评分信息和热门类型等统计信息。

## 接口详情

### 获取网站统计数据

**接口地址**: `GET /stats`

**接口描述**: 获取网站整体统计数据

**请求参数**: 无

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalMovies": 30,
    "totalTVShows": 15,
    "totalVarietyShows": 17,
    "totalUsers": 11,
    "totalViews": 139300000,
    "monthlyNewContent": 62,
    "averageRating": 8.65,
    "topGenres": [
      {"name": "剧情", "count": 16},
      {"name": "科幻", "count": 15},
      {"name": "冒险", "count": 13}
    ]
  }
}
```

**响应字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalMovies | Long | 电影总数 |
| totalTVShows | Long | 电视剧总数 |
| totalVarietyShows | Long | 综艺节目总数 |
| totalUsers | Long | 用户总数 |
| totalViews | Long | 总观看数 |
| monthlyNewContent | Long | 本月新增内容数量 |
| averageRating | BigDecimal | 平均评分 |
| topGenres | List<TopGenre> | 热门类型（前3名） |

**TopGenre 对象说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| name | String | 类型名称 |
| count | Long | 该类型的内容数量 |

## 使用示例

### cURL 请求示例

```bash
curl -X GET "http://localhost:8080/stats" \
     -H "Content-Type: application/json"
```

### JavaScript 请求示例

```javascript
fetch('/stats')
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      console.log('统计数据:', data.data);
      console.log('电影总数:', data.data.totalMovies);
      console.log('热门类型:', data.data.topGenres);
    }
  })
  .catch(error => console.error('请求失败:', error));
```

## 技术实现

### 架构层次

1. **Controller层**: `StatsController` - 处理HTTP请求和响应
2. **Service层**: `StatsService` - 业务逻辑处理
3. **Mapper层**: `StatsMapper` - 数据访问层
4. **Entity层**: `Stats`, `TopGenre` - 数据实体

### 数据库查询

统计接口通过以下SQL查询获取数据：

- **内容统计**: 统计各类型内容的数量（status = 1 的有效内容）
- **用户统计**: 统计有效用户数量
- **观看统计**: 汇总所有内容的观看数
- **月度统计**: 统计当月新增的内容数量
- **评分统计**: 计算所有内容的平均评分
- **类型统计**: 统计各类型的内容数量，返回前3名

### 错误处理

接口包含完善的错误处理机制：

- 数据库连接异常
- 查询执行异常
- 数据处理异常

所有异常都会返回统一的错误响应格式。

## 注意事项

1. 该接口为公开接口，无需身份验证
2. 统计数据实时计算，反映当前数据库状态
3. 热门类型统计基于内容的genre字段进行模糊匹配
4. 月度新增内容统计基于created_at字段的年月匹配
5. 平均评分计算排除了null值的记录

## 性能考虑

- 统计查询涉及多表联合查询，在大数据量情况下可能需要优化
- 建议在相关字段上建立索引以提高查询性能
- 可考虑添加缓存机制减少数据库查询频率

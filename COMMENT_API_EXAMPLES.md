# 评论接口测试示例

## 1. 获取评论列表接口

### 1.1 获取电影评论列表

```bash
# 获取电影评论列表（默认参数）
curl -X GET "http://localhost:8080/comments/movie/1"

# 获取电影评论列表（带分页和排序）
curl -X GET "http://localhost:8080/comments/movie/1?page=1&limit=5&sort=hottest"
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "comments": [
      {
        "id": 1,
        "userId": 2,
        "contentId": 1,
        "contentType": "movie",
        "parentId": null,
        "content": "这部电影太棒了！视觉效果震撼，故事情节也很吸引人。",
        "rating": 5,
        "likes": 24,
        "status": 1,
        "createdAt": "2024-01-15T21:30:00",
        "updatedAt": "2025-06-16T20:49:49.584237",
        "username": "user001",
        "avatar": "https://example.com/avatar1.jpg",
        "replies": [
          {
            "id": 4,
            "userId": 5,
            "contentId": 1,
            "contentType": "movie",
            "parentId": 1,
            "content": "同感！特别是水下的场景，太美了！",
            "rating": null,
            "likes": 8,
            "status": 1,
            "createdAt": "2024-01-16T11:30:00",
            "updatedAt": "2025-06-16T20:45:17.282507",
            "username": "user004",
            "avatar": "https://example.com/avatar4.jpg",
            "replies": null
          }
        ]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 4,
      "totalPages": 1
    }
  }
}
```

### 1.2 获取电视剧评论列表

```bash
# 获取电视剧评论列表
curl -X GET "http://localhost:8080/comments/tv/1?sort=latest"
```

### 1.3 获取综艺节目评论列表

```bash
# 获取综艺节目评论列表
curl -X GET "http://localhost:8080/comments/variety/1?page=1&limit=3"
```

## 2. 发表评论接口

### 2.1 用户认证

```bash
# 注册新用户
curl -X POST "http://localhost:8080/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "commentuser",
    "password": "password123",
    "confirmPassword": "password123",
    "email": "<EMAIL>"
  }'

# 用户登录获取token
curl -X POST "http://localhost:8080/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "commentuser",
    "password": "password123"
  }'
```

**登录响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 7,
    "username": "commentuser",
    "email": "<EMAIL>",
    "avatar": null,
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 86400
  }
}
```

### 2.2 发表主评论

```bash
# 发表电影评论（带评分）
curl -X POST "http://localhost:8080/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "contentId": 1,
    "contentType": "movie",
    "content": "这部电影真的很棒！特效和剧情都很出色。",
    "rating": 5
  }'

# 发表电视剧评论
curl -X POST "http://localhost:8080/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "contentId": 1,
    "contentType": "tv",
    "content": "这部剧太精彩了，演员演技都很棒！",
    "rating": 5
  }'
```

**发表评论响应示例**:
```json
{
  "code": 200,
  "message": "评论成功",
  "data": {
    "commentId": 35,
    "content": "这部电影真的很棒！特效和剧情都很出色。",
    "rating": 5,
    "createdAt": "2025-06-16T20:49:02.244179"
  }
}
```

### 2.3 回复评论

```bash
# 回复某个评论
curl -X POST "http://localhost:8080/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "contentId": 1,
    "contentType": "movie",
    "content": "我也同意！卡梅隆的作品总是让人惊艳。",
    "parentId": 1
  }'
```

**回复评论响应示例**:
```json
{
  "code": 200,
  "message": "评论成功",
  "data": {
    "commentId": 36,
    "content": "我也同意！卡梅隆的作品总是让人惊艳。",
    "rating": null,
    "createdAt": "2025-06-16T20:49:20.53828"
  }
}
```

## 3. 点赞评论接口

```bash
# 点赞评论
curl -X POST "http://localhost:8080/comments/1/like" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**点赞响应示例**:
```json
{
  "code": 200,
  "message": "点赞成功",
  "data": null
}
```

## 4. 删除评论接口

```bash
# 删除自己的评论
curl -X DELETE "http://localhost:8080/comments/35" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**删除响应示例**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 5. 错误处理示例

### 5.1 无效的内容类型

```bash
curl -X GET "http://localhost:8080/comments/invalid/1"
```

**错误响应**:
```json
{
  "code": 400,
  "message": "不支持的内容类型: invalid",
  "data": null
}
```

### 5.2 未提供认证token

```bash
curl -X POST "http://localhost:8080/comments" \
  -H "Content-Type: application/json" \
  -d '{
    "contentId": 1,
    "contentType": "movie",
    "content": "测试评论"
  }'
```

**错误响应**:
```json
{
  "code": 401,
  "message": "未授权访问，请先登录",
  "data": null
}
```

### 5.3 无效的认证token

```bash
curl -X POST "http://localhost:8080/comments" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer invalid_token" \
  -d '{
    "contentId": 1,
    "contentType": "movie",
    "content": "测试评论"
  }'
```

**错误响应**:
```json
{
  "code": 401,
  "message": "无效的认证token",
  "data": null
}
```

## 6. 数据库示例数据

系统已包含以下评论示例数据：

### 6.1 电影评论
- **阿凡达：水之道**: 3条主评论，5条回复
- **流浪地球2**: 3条主评论，2条回复

### 6.2 电视剧评论
- **狂飙**: 3条主评论，2条回复
- **三体**: 3条主评论，1条回复

### 6.3 综艺节目评论
- **向往的生活**: 3条主评论，2条回复
- **奔跑吧**: 3条主评论，1条回复
- **明星大侦探**: 3条主评论，2条回复

## 7. 技术架构

### 7.1 四层架构

- **控制层**: `CommentController` - 处理HTTP请求
- **服务层**: `CommentService/CommentServiceImpl` - 业务逻辑处理
- **数据访问层**: `CommentMapper` - 数据库操作
- **实体层**: `Comment/CommentResponse/CommentRequest` - 数据实体

### 7.2 数据库表结构

```sql
-- 评论表
CREATE TABLE comment (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    content_id BIGINT NOT NULL,
    content_type VARCHAR(20) NOT NULL, -- movie, tv, variety
    parent_id BIGINT DEFAULT NULL, -- 父评论ID，用于回复
    content TEXT NOT NULL, -- 评论内容
    rating INT DEFAULT NULL, -- 评分（1-5），只有主评论才有评分
    likes INT DEFAULT 0, -- 点赞数
    status TINYINT DEFAULT 1, -- 状态：1-正常，0-删除
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES "user"(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES comment(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_comment_content ON comment (content_id, content_type);
CREATE INDEX idx_comment_parent ON comment (parent_id);
CREATE INDEX idx_comment_created_at ON comment (created_at);
```

## 8. 特性

- ✅ 支持电影、电视剧、综艺节目评论
- ✅ 支持主评论和回复评论（嵌套结构）
- ✅ 支持评分功能（1-5星，仅主评论）
- ✅ 支持点赞功能
- ✅ 支持分页和排序（最新、最热）
- ✅ JWT认证保护写操作
- ✅ 完整的错误处理和参数验证
- ✅ 软删除机制
- ✅ 用户信息关联显示
- ✅ Spring Boot四层架构设计
- ✅ H2数据库存储评论数据

## 9. 测试覆盖

### 9.1 单元测试

- **CommentControllerTest**: 8个测试用例，覆盖所有控制器方法

### 9.2 集成测试

- 应用程序启动测试
- 数据库连接和数据初始化测试
- API端到端测试

### 9.3 测试结果

```
Tests run: 8, Failures: 0, Errors: 0, Skipped: 0
BUILD SUCCESS
```

## 10. 完成状态

✅ **评论接口实现完成**

- [x] 获取评论列表接口 (`GET /comments/{type}/{id}`)
- [x] 发表评论接口 (`POST /comments`)
- [x] 点赞评论接口 (`POST /comments/{id}/like`)
- [x] 删除评论接口 (`DELETE /comments/{id}`)
- [x] 支持电影、电视剧、综艺节目
- [x] 支持嵌套回复结构
- [x] 支持评分和点赞功能
- [x] 支持分页和排序
- [x] JWT认证保护
- [x] 完整的错误处理
- [x] 单元测试覆盖
- [x] 数据库示例数据
- [x] API文档和使用示例

**评论接口功能已完全实现并通过测试！**

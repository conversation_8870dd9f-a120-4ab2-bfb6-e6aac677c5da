# 电视剧API文档

## 概述

本文档描述了电视剧模块的API接口，基于Spring Boot四层架构实现，使用H2数据库存储数据。

## 数据库设计

### 电视剧表 (tv_series)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| title | VARCHAR(255) | 电视剧标题 |
| poster | VARCHAR(500) | 海报图片URL |
| backdrop | VARCHAR(500) | 背景图片URL |
| rating | DECIMAL(3,1) | 评分 |
| series_year | INT | 年份 |
| genre | VARCHAR(500) | 类型（JSON格式） |
| region | VARCHAR(50) | 地区 |
| director | VARCHAR(255) | 导演 |
| cast | TEXT | 演员（JSON格式） |
| description | TEXT | 描述 |
| series_status | VARCHAR(50) | 状态：ongoing/completed/upcoming |
| current_episode | INT | 当前集数 |
| total_episodes | INT | 总集数 |
| update_day | VARCHAR(20) | 更新日期 |
| update_time | VARCHAR(10) | 更新时间 |
| first_air_date | DATE | 首播日期 |
| view_count | BIGINT | 观看次数 |
| like_count | BIGINT | 点赞次数 |
| status | TINYINT | 记录状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

### 剧集表 (tv_series_episode)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键，自增 |
| series_id | BIGINT | 电视剧ID |
| episode_number | INT | 集数 |
| title | VARCHAR(255) | 集标题 |
| duration | INT | 时长（分钟） |
| play_url | VARCHAR(500) | 播放地址 |
| status | TINYINT | 记录状态：1-启用，0-禁用 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

## API接口

### 1. 获取电视剧列表

**接口地址**: `GET /tv-series`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认12 |
| genre | string | 否 | 类型筛选 |
| region | string | 否 | 地区筛选：cn,kr,us,jp,uk |
| status | string | 否 | 状态筛选：ongoing,completed,upcoming |
| sort | string | 否 | 排序方式：latest,rating,popular,name |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "狂飙",
        "poster": "https://example.com/tv1.jpg",
        "backdrop": "https://example.com/tv1_bg.jpg",
        "rating": 9.1,
        "year": 2023,
        "genre": ["剧情", "犯罪", "悬疑"],
        "region": "cn",
        "description": "一部反映扫黑除恶的现实主义力作...",
        "status": "completed",
        "currentEpisode": 39,
        "totalEpisodes": 39,
        "updateDay": "周五",
        "updateTime": "20:00",
        "firstAirDate": "2023-01-14",
        "viewCount": 2500000,
        "likeCount": 180000
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 10,
      "totalPages": 1
    }
  }
}
```

### 2. 获取电视剧详情

**接口地址**: `GET /tv-series/{id}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 电视剧ID |

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "狂飙",
    "poster": "https://example.com/tv1.jpg",
    "backdrop": "https://example.com/tv1_bg.jpg",
    "rating": 9.1,
    "year": 2023,
    "genre": ["剧情", "犯罪", "悬疑"],
    "region": "cn",
    "director": "徐纪周",
    "cast": ["张译", "张颂文", "李一桐"],
    "description": "一部反映扫黑除恶的现实主义力作...",
    "status": "completed",
    "currentEpisode": 39,
    "totalEpisodes": 39,
    "updateDay": "周五",
    "updateTime": "20:00",
    "firstAirDate": "2023-01-14",
    "episodes": [
      {
        "episode": 1,
        "title": "第1集",
        "duration": 45,
        "playUrl": "https://example.com/tv1_ep1.m3u8"
      }
    ]
  }
}
```

## 3. 电视剧管理接口

### 3.1 获取所有电视剧（管理用）

**接口地址**: `GET /tv-series/admin/all`

**描述**: 获取所有电视剧列表（包括已删除的），用于管理后台

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认12 |

**请求示例**:
```bash
curl -X GET "http://localhost:8080/tv-series/admin/all?page=1&limit=20"
```

**响应示例**: 同获取电视剧列表

### 3.2 创建电视剧

**接口地址**: `POST /tv-series`

**描述**: 创建新的电视剧记录

**请求体参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | string | 是 | 电视剧标题 |
| poster | string | 否 | 海报图片URL |
| backdrop | string | 否 | 背景图片URL |
| rating | decimal | 否 | 评分，默认0.0 |
| seriesYear | int | 否 | 年份 |
| genre | string | 否 | 类型（JSON格式） |
| region | string | 否 | 地区 |
| director | string | 否 | 导演 |
| cast | string | 否 | 演员（JSON格式） |
| description | string | 否 | 电视剧描述 |
| seriesStatus | string | 否 | 状态，默认"ongoing" |
| currentEpisode | int | 否 | 当前集数，默认1 |
| totalEpisodes | int | 否 | 总集数 |
| updateDay | string | 否 | 更新日期 |
| updateTime | string | 否 | 更新时间 |
| firstAirDate | date | 否 | 首播日期 |

**请求示例**:
```bash
curl -X POST http://localhost:8080/tv-series \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新电视剧",
    "poster": "https://example.com/new-tv-poster.jpg",
    "backdrop": "https://example.com/new-tv-backdrop.jpg",
    "rating": 8.0,
    "seriesYear": 2024,
    "genre": "[\"剧情\", \"爱情\"]",
    "region": "cn",
    "director": "知名导演",
    "cast": "[\"演员A\", \"演员B\"]",
    "description": "一部精彩的新电视剧",
    "seriesStatus": "upcoming",
    "totalEpisodes": 30,
    "updateDay": "周三",
    "updateTime": "20:00",
    "firstAirDate": "2024-06-01"
  }'
```

### 3.3 更新电视剧

**接口地址**: `PUT /tv-series/{id}`

**描述**: 更新指定ID的电视剧信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 电视剧ID |

**请求体参数**: 同创建接口

**请求示例**:
```bash
curl -X PUT http://localhost:8080/tv-series/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的电视剧标题",
    "rating": 9.0,
    "seriesStatus": "completed",
    "currentEpisode": 30,
    "totalEpisodes": 30
  }'
```

### 3.4 删除电视剧

**接口地址**: `DELETE /tv-series/{id}`

**描述**: 删除指定ID的电视剧（软删除）

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 电视剧ID |

**请求示例**:
```bash
curl -X DELETE http://localhost:8080/tv-series/1
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": "电视剧删除成功"
}
```

## 示例数据

数据库中包含了10部电视剧的示例数据，包括：
- 狂飙（2023年，犯罪剧情）
- 三体（2023年，科幻剧情）
- 去有风的地方（2023年，都市治愈）
- 梦华录（2022年，古装爱情）
- 人世间（2022年，年代剧情）
- 庆余年2（2024年，古装喜剧，正在播出）
- 繁花（2023年，年代商战）
- 长月烬明（2023年，古装仙侠）
- 以爱为营（2023年，都市爱情）
- 莲花楼（2023年，古装武侠）

## 测试示例

### 查询接口测试
```bash
# 获取电视剧列表
curl -X GET "http://localhost:8080/tv-series?page=1&limit=5"

# 获取电视剧详情
curl -X GET "http://localhost:8080/tv-series/1"

# 筛选已完结的电视剧，按评分排序
curl -X GET "http://localhost:8080/tv-series?status=completed&sort=rating"

# 筛选正在播出的电视剧
curl -X GET "http://localhost:8080/tv-series?status=ongoing"
```

### 管理接口测试
```bash
# 获取所有电视剧（管理用）
curl -X GET "http://localhost:8080/tv-series/admin/all?page=1&limit=10"

# 创建新电视剧
curl -X POST http://localhost:8080/tv-series \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试电视剧",
    "rating": 8.5,
    "seriesYear": 2024,
    "genre": "[\"剧情\", \"爱情\"]",
    "region": "cn",
    "director": "测试导演",
    "description": "这是一部测试电视剧",
    "seriesStatus": "upcoming",
    "totalEpisodes": 24
  }'

# 更新电视剧信息
curl -X PUT http://localhost:8080/tv-series/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "rating": 9.0,
    "seriesStatus": "ongoing"
  }'

# 删除电视剧
curl -X DELETE http://localhost:8080/tv-series/1
```

## 技术架构

- **控制层**: TvSeriesController - 处理HTTP请求
- **服务层**: TvSeriesService/TvSeriesServiceImpl - 业务逻辑处理
- **数据访问层**: TvSeriesMapper/TvSeriesEpisodeMapper - 数据库操作
- **实体层**: TvSeries/TvSeriesEpisode - 数据实体

## 特性

### 查询功能
- 支持分页查询
- 支持多条件筛选（类型、地区、状态）
- 支持多种排序方式
- 包含剧集详细信息
- JSON格式存储复杂数据（类型、演员）

### 管理功能
- 完整的CRUD操作（创建、读取、更新、删除）
- 软删除机制，保护数据完整性
- 参数验证和错误处理
- 默认值自动设置
- 管理员专用接口

### 技术特性
- RESTful API设计
- 统一的响应格式
- 完整的错误处理
- Spring Boot四层架构
- MyBatis注解式SQL
- H2内存数据库
